import { BaseEntity } from '../../base/entity/base';
import { Column, Entity, Index, ManyToOne, JoinColumn } from 'typeorm';
import { GameThemeEntity } from './theme';
import { GameMemberEntity } from './member';

/**
 * 游戏场次实体
 */
@Entity('game_session')
export class GameSessionEntity extends BaseEntity {
  @Index()
  @Column({ comment: '主题ID' })
  themeId: number;

  @Column({ comment: '玩家姓名（匿名处理）', length: 50, nullable: true })
  playerName: string;

  @Column({ comment: '联系电话', length: 20, nullable: true })
  phone: string;

  @Column({ comment: '游戏人数' })
  playerCount: number;

  @Column({ comment: '开始时间', type: 'datetime' })
  startTime: Date;

  @Column({ comment: '结束时间', type: 'datetime' })
  endTime: Date;

  @Column({
    comment: '实际价格',
    type: 'decimal',
    precision: 10,
    scale: 2, nullable: true
  })
  actualPrice: number;

  @Column({ comment: '支付方式 1-现金 2-会员余额 3-第三方券', default: 1 })
  paymentType: number;

  @Column({ comment: '支付状态 0-未支付 1-已支付', default: 0 })
  paymentStatus: number;

  @Column({ comment: '会员ID', nullable: true })
  memberId: number;

  @Column({ comment: '第三方券码', nullable: true, length: 100 })
  couponCode: string;

  @Column({ comment: '游戏状态 0-待开始 1-进行中 2-已结束 3-已取消', default: 0 })
  status: number;

  @Column({ comment: '入场码', length: 20, nullable: true })
  entryCode: string;

  @Column({ comment: '备注', nullable: true })
  remark: string;

  @Column({ comment: '操作员ID', nullable: true })
  operatorId: number;

  // 关系定义
  @ManyToOne(() => GameThemeEntity)
  @JoinColumn({ name: 'themeId' })
  theme: GameThemeEntity;

  @ManyToOne(() => GameMemberEntity)
  @JoinColumn({ name: 'memberId' })
  member: GameMemberEntity;
}
