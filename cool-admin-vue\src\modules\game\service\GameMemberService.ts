import { BaseService } from '/@/cool';

// 游戏会员服务
export default class GameMemberService extends BaseService {
	namespace = '/admin/game/member';

	// 充值
	recharge(data: { id: number; amount: number; remark?: string }) {
		return this.request({
			url: '/recharge',
			method: 'post',
			data
		});
	}

	// 消费
	consume(data: { id: number; amount: number; remark?: string }) {
		return this.request({
			url: '/consume',
			method: 'post',
			data
		});
	}

	// 获取交易记录
	getTransactions(params: { memberId: number; page?: number; size?: number }) {
		return this.request({
			url: '/transactions',
			method: 'get',
			params
		});
	}
}
