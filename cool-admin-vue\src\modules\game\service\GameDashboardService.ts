import { BaseService } from '/@/cool';

// 游戏仪表板服务
export default class GameDashboardService extends BaseService {
	namespace = '/admin/game/dashboard';

	// 获取仪表板数据
	getDashboardData() {
		return this.request({
			url: '/data',
			method: 'get'
		});
	}

	// 获取游戏状态
	getGameStatus() {
		return this.request({
			url: '/gameStatus',
			method: 'get'
		});
	}

	// 获取实时统计
	getRealTimeStats() {
		return this.request({
			url: '/realTimeStats',
			method: 'get'
		});
	}
}
