import { BaseService } from '/@/cool';

// 游戏主题服务
export default class GameThemeService extends BaseService {
	namespace = '/admin/game/theme';

	// 获取主题状态
	getThemeStatus() {
		return this.request({
			url: '/status',
			method: 'get'
		});
	}

	// 设置主题状态
	setThemeStatus(data: { id: number; status: number }) {
		return this.request({
			url: '/setStatus',
			method: 'post',
			data
		});
	}
}

// 游戏场次服务
export default class GameSessionService extends BaseService {
	namespace = '/admin/game/session';

	// 开始游戏
	startGame(data: { id: number }) {
		return this.request({
			url: '/startGame',
			method: 'post',
			data
		});
	}

	// 结束游戏
	endGame(data: { id: number }) {
		return this.request({
			url: '/endGame',
			method: 'post',
			data
		});
	}

	// 取消游戏
	cancelGame(data: { id: number }) {
		return this.request({
			url: '/cancelGame',
			method: 'post',
			data
		});
	}
}

// 游戏预约服务
export default class GameReservationService extends BaseService {
	namespace = '/admin/game/reservation';

	// 获取即将开始的预约
	getUpcomingReservations() {
		return this.request({
			url: '/upcoming',
			method: 'get'
		});
	}

	// 确认预约
	confirmReservation(data: { id: number }) {
		return this.request({
			url: '/confirm',
			method: 'post',
			data
		});
	}

	// 取消预约
	cancelReservation(data: { id: number }) {
		return this.request({
			url: '/cancel',
			method: 'post',
			data
		});
	}
}

// 游戏会员服务
export default class GameMemberService extends BaseService {
	namespace = '/admin/game/member';

	// 充值
	recharge(data: { id: number; amount: number; remark?: string }) {
		return this.request({
			url: '/recharge',
			method: 'post',
			data
		});
	}

	// 消费
	consume(data: { id: number; amount: number; remark?: string }) {
		return this.request({
			url: '/consume',
			method: 'post',
			data
		});
	}

	// 获取交易记录
	getTransactions(params: { memberId: number; page?: number; size?: number }) {
		return this.request({
			url: '/transactions',
			method: 'get',
			params
		});
	}
}

// 游戏仪表板服务
export default class GameDashboardService extends BaseService {
	namespace = '/admin/game/dashboard';

	// 获取仪表板数据
	getDashboardData() {
		return this.request({
			url: '/data',
			method: 'get'
		});
	}

	// 获取游戏状态
	getGameStatus() {
		return this.request({
			url: '/gameStatus',
			method: 'get'
		});
	}

	// 获取实时统计
	getRealTimeStats() {
		return this.request({
			url: '/realTimeStats',
			method: 'get'
		});
	}
}

// 游戏优惠券服务
export default class GameCouponService extends BaseService {
	namespace = '/admin/game/coupon';

	// 验证优惠券
	verifyCoupon(data: { code: string }) {
		return this.request({
			url: '/verify',
			method: 'post',
			data
		});
	}

	// 使用优惠券
	useCoupon(data: { code: string; sessionId: number }) {
		return this.request({
			url: '/use',
			method: 'post',
			data
		});
	}
}

// 游戏报表服务
export default class GameReportService extends BaseService {
	namespace = '/admin/game/report';

	// 获取营收报表
	getRevenueReport(params: { startDate: string; endDate: string }) {
		return this.request({
			url: '/revenue',
			method: 'get',
			params
		});
	}

	// 获取主题统计
	getThemeStats(params: { startDate: string; endDate: string }) {
		return this.request({
			url: '/themeStats',
			method: 'get',
			params
		});
	}

	// 获取会员统计
	getMemberStats(params: { startDate: string; endDate: string }) {
		return this.request({
			url: '/memberStats',
			method: 'get',
			params
		});
	}
}
