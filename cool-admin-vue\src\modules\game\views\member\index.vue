<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn />
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />
			<cl-flex1 />
			<!-- 关键字搜索 -->
			<cl-search-key />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table">
				<!-- 会员头像 -->
				<template #column-avatar="{ scope }">
					<el-avatar
						:src="scope.row.avatar"
						:size="40"
					>
						{{ scope.row.name?.charAt(0) }}
					</el-avatar>
				</template>

				<!-- 余额 -->
				<template #column-balance="{ scope }">
					<span style="color: #67c23a; font-weight: bold;">¥{{ scope.row.balance }}</span>
				</template>

				<!-- 状态 -->
				<template #column-status="{ scope }">
					<el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
						{{ scope.row.status === 1 ? '正常' : '禁用' }}
					</el-tag>
				</template>

				<!-- 操作按钮 -->
				<template #slot-btns="{ scope }">
					<el-button-group>
						<el-button size="small" @click="editMember(scope.row)">编辑</el-button>
						<el-button size="small" type="primary" @click="recharge(scope.row)">充值</el-button>
						<el-button size="small" type="warning" @click="consume(scope.row)">消费</el-button>
						<el-button size="small" type="info" @click="viewTransactions(scope.row)">交易记录</el-button>
						<el-button 
							size="small" 
							:type="scope.row.status === 1 ? 'danger' : 'success'"
							@click="toggleStatus(scope.row)"
						>
							{{ scope.row.status === 1 ? '禁用' : '启用' }}
						</el-button>
					</el-button-group>
				</template>
			</cl-table>
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert">
			<template #slot-avatar="{ scope }">
				<cl-upload-space
					:modelValue="scope.avatar"
					@update:modelValue="scope.avatar = $event"
					:limit="1"
					accept="image/*"
				/>
			</template>
		</cl-upsert>

		<!-- 充值对话框 -->
		<el-dialog v-model="rechargeVisible" title="会员充值" width="400px">
			<el-form :model="rechargeForm" label-width="80px">
				<el-form-item label="会员姓名">
					<el-input v-model="rechargeForm.memberName" disabled />
				</el-form-item>
				<el-form-item label="当前余额">
					<el-input v-model="rechargeForm.currentBalance" disabled />
				</el-form-item>
				<el-form-item label="充值金额" required>
					<el-input-number
						v-model="rechargeForm.amount"
						:min="0.01"
						:precision="2"
						style="width: 100%"
						placeholder="请输入充值金额"
					/>
				</el-form-item>
				<el-form-item label="备注">
					<el-input
						v-model="rechargeForm.remark"
						type="textarea"
						rows="3"
						placeholder="请输入备注信息"
					/>
				</el-form-item>
			</el-form>
			<template #footer>
				<el-button @click="rechargeVisible = false">取消</el-button>
				<el-button type="primary" @click="confirmRecharge">确认充值</el-button>
			</template>
		</el-dialog>

		<!-- 消费对话框 -->
		<el-dialog v-model="consumeVisible" title="会员消费" width="400px">
			<el-form :model="consumeForm" label-width="80px">
				<el-form-item label="会员姓名">
					<el-input v-model="consumeForm.memberName" disabled />
				</el-form-item>
				<el-form-item label="当前余额">
					<el-input v-model="consumeForm.currentBalance" disabled />
				</el-form-item>
				<el-form-item label="消费金额" required>
					<el-input-number
						v-model="consumeForm.amount"
						:min="0.01"
						:max="consumeForm.maxAmount"
						:precision="2"
						style="width: 100%"
						placeholder="请输入消费金额"
					/>
				</el-form-item>
				<el-form-item label="备注">
					<el-input
						v-model="consumeForm.remark"
						type="textarea"
						rows="3"
						placeholder="请输入备注信息"
					/>
				</el-form-item>
			</el-form>
			<template #footer>
				<el-button @click="consumeVisible = false">取消</el-button>
				<el-button type="primary" @click="confirmConsume">确认消费</el-button>
			</template>
		</el-dialog>

		<!-- 交易记录对话框 -->
		<el-dialog v-model="transactionVisible" title="交易记录" width="800px">
			<el-table :data="transactionList" style="width: 100%">
				<el-table-column prop="type" label="类型" width="80">
					<template #default="{ row }">
						<el-tag :type="row.type === 1 ? 'success' : 'warning'">
							{{ row.type === 1 ? '充值' : '消费' }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="amount" label="金额" width="100">
					<template #default="{ row }">
						<span :style="{ color: row.type === 1 ? '#67c23a' : '#f56c6c' }">
							{{ row.type === 1 ? '+' : '-' }}¥{{ row.amount }}
						</span>
					</template>
				</el-table-column>
				<el-table-column prop="balanceAfter" label="余额" width="100">
					<template #default="{ row }">
						¥{{ row.balanceAfter }}
					</template>
				</el-table-column>
				<el-table-column prop="remark" label="备注" min-width="150" />
				<el-table-column prop="createTime" label="时间" width="160">
					<template #default="{ row }">
						{{ formatTime(row.createTime) }}
					</template>
				</el-table-column>
			</el-table>
		</el-dialog>
	</cl-crud>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { useCrud, useTable, useUpsert } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { ElMessage, ElMessageBox } from 'element-plus';

defineOptions({
	name: 'game-member'
});

const { service } = useCool();

// 充值对话框
const rechargeVisible = ref(false);
const rechargeForm = reactive({
	memberId: 0,
	memberName: '',
	currentBalance: '',
	amount: 0,
	remark: ''
});

// 消费对话框
const consumeVisible = ref(false);
const consumeForm = reactive({
	memberId: 0,
	memberName: '',
	currentBalance: '',
	maxAmount: 0,
	amount: 0,
	remark: ''
});

// 交易记录对话框
const transactionVisible = ref(false);
const transactionList = ref([]);

// cl-crud 配置
const Crud = useCrud(
	{
		service: service.game.member
	},
	(app) => {
		app.refresh();
	}
);

// cl-table 配置
const Table = useTable({
	columns: [
		{
			type: 'selection',
			width: 60
		},
		{
			label: '头像',
			prop: 'avatar',
			width: 80
		},
		{
			label: '姓名',
			prop: 'name',
			width: 120
		},
		{
			label: '电话',
			prop: 'phone',
			width: 130
		},
		{
			label: '余额',
			prop: 'balance',
			width: 100
		},
		{
			label: '积分',
			prop: 'points',
			width: 80
		},
		{
			label: '状态',
			prop: 'status',
			width: 80
		},
		{
			label: '注册时间',
			prop: 'createTime',
			width: 160,
			sortable: 'desc'
		},
		{
			type: 'op',
			buttons: ['slot-btns'],
			width: 350
		}
	]
});

// cl-upsert 配置
const Upsert = useUpsert({
	items: [
		{
			label: '头像',
			prop: 'avatar',
			component: {
				name: 'slot-avatar'
			}
		},
		{
			label: '姓名',
			prop: 'name',
			required: true,
			component: {
				name: 'el-input',
				props: {
					placeholder: '请输入会员姓名'
				}
			}
		},
		{
			label: '电话',
			prop: 'phone',
			required: true,
			component: {
				name: 'el-input',
				props: {
					placeholder: '请输入手机号码'
				}
			}
		},
		{
			label: '初始余额',
			prop: 'balance',
			value: 0,
			component: {
				name: 'el-input-number',
				props: {
					min: 0,
					precision: 2,
					placeholder: '初始余额'
				}
			}
		},
		{
			label: '状态',
			prop: 'status',
			value: 1,
			component: {
				name: 'el-radio-group',
				options: [
					{ label: '正常', value: 1 },
					{ label: '禁用', value: 0 }
				]
			}
		}
	]
});

// 格式化时间
const formatTime = (time: string) => {
	if (!time) return '-';
	return new Date(time).toLocaleString('zh-CN');
};

// 编辑会员
const editMember = (row: any) => {
	Upsert.value?.open({
		type: 'update',
		data: row
	});
};

// 充值
const recharge = (row: any) => {
	rechargeForm.memberId = row.id;
	rechargeForm.memberName = row.name;
	rechargeForm.currentBalance = `¥${row.balance}`;
	rechargeForm.amount = 0;
	rechargeForm.remark = '';
	rechargeVisible.value = true;
};

// 确认充值
const confirmRecharge = async () => {
	if (!rechargeForm.amount || rechargeForm.amount <= 0) {
		ElMessage.error('请输入有效的充值金额');
		return;
	}

	try {
		await service.game.member.recharge({
			id: rechargeForm.memberId,
			amount: rechargeForm.amount,
			remark: rechargeForm.remark
		});

		ElMessage.success('充值成功');
		rechargeVisible.value = false;
		Crud.value?.refresh();
	} catch (error) {
		ElMessage.error('充值失败');
	}
};

// 消费
const consume = (row: any) => {
	consumeForm.memberId = row.id;
	consumeForm.memberName = row.name;
	consumeForm.currentBalance = `¥${row.balance}`;
	consumeForm.maxAmount = row.balance;
	consumeForm.amount = 0;
	consumeForm.remark = '';
	consumeVisible.value = true;
};

// 确认消费
const confirmConsume = async () => {
	if (!consumeForm.amount || consumeForm.amount <= 0) {
		ElMessage.error('请输入有效的消费金额');
		return;
	}

	if (consumeForm.amount > consumeForm.maxAmount) {
		ElMessage.error('消费金额不能超过当前余额');
		return;
	}

	try {
		await service.game.member.consume({
			id: consumeForm.memberId,
			amount: consumeForm.amount,
			remark: consumeForm.remark
		});

		ElMessage.success('消费成功');
		consumeVisible.value = false;
		Crud.value?.refresh();
	} catch (error) {
		ElMessage.error('消费失败');
	}
};

// 查看交易记录
const viewTransactions = async (row: any) => {
	try {
		const res = await service.game.member.getTransactions({
			memberId: row.id
		});
		transactionList.value = res.list || [];
		transactionVisible.value = true;
	} catch (error) {
		ElMessage.error('获取交易记录失败');
	}
};

// 切换状态
const toggleStatus = async (row: any) => {
	const newStatus = row.status === 1 ? 0 : 1;
	const statusText = newStatus === 1 ? '启用' : '禁用';
	
	try {
		await ElMessageBox.confirm(
			`确定要${statusText}会员"${row.name}"吗？`,
			'状态切换',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}
		);

		await service.game.member.update({
			id: row.id,
			status: newStatus
		});

		ElMessage.success(`${statusText}成功`);
		Crud.value?.refresh();
	} catch (error) {
		if (error !== 'cancel') {
			ElMessage.error(`${statusText}失败`);
		}
	}
};
</script>

<style lang="scss" scoped>
.el-button-group {
	.el-button {
		margin-left: 0;
	}
}
</style>
