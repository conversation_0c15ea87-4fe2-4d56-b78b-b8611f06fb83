"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminGameSessionController = void 0;
const core_1 = require("@midwayjs/core");
const core_2 = require("@cool-midway/core");
const session_1 = require("../../entity/session");
const session_2 = require("../../service/session");
const session_3 = require("../../dto/session");
const theme_1 = require("../../entity/theme");
/**
 * 游戏场次管理
 */
let AdminGameSessionController = class AdminGameSessionController extends core_2.BaseController {
    /**
     * 开始游戏
     */
    async startGame(dto) {
        return this.ok(await this.gameSessionService.startGame(dto));
    }
    /**
     * 开始游戏计时
     */
    async startGameTimer(body) {
        return this.ok(await this.gameSessionService.startGameTimer(body.sessionId));
    }
    /**
     * 结束游戏
     */
    async endGame(body) {
        return this.ok(await this.gameSessionService.endGame(body.sessionId));
    }
    /**
     * 取消游戏
     */
    async cancelGame(body) {
        const { sessionId, reason } = body;
        return this.ok(await this.gameSessionService.cancelGame(sessionId, reason));
    }
    /**
     * 获取当前进行中的游戏
     */
    async getActiveGames() {
        return this.ok(await this.gameSessionService.getActiveGames());
    }
    /**
     * 获取今日游戏统计
     */
    async getTodayStats() {
        return this.ok(await this.gameSessionService.getTodayStats());
    }
    /**
     * 创建游戏场次
     */
    async createSession(dto) {
        return this.ok(await this.gameSessionService.add(dto));
    }
    /**
     * 更新游戏场次
     */
    async updateSession(dto) {
        return this.ok(await this.gameSessionService.update(dto));
    }
    /**
     * 获取游戏场次详情（包含关联信息）
     */
    async getDetail(id) {
        const session = await this.gameSessionService.info(id);
        if (session) {
            // 获取关联的主题信息
            const theme = await this.gameSessionService.getThemeInfo(session.themeId);
            session.theme = theme;
            // 如果有会员ID，获取会员信息
            if (session.memberId) {
                const member = await this.gameSessionService.getMemberInfo(session.memberId);
                session.member = member;
            }
        }
        return this.ok(session);
    }
    /**
     * 批量操作游戏场次
     */
    async batchOperation(body) {
        const { operation, sessionIds, reason } = body;
        const results = [];
        for (const sessionId of sessionIds) {
            try {
                let result;
                switch (operation) {
                    case 'start':
                        result = await this.gameSessionService.startGameTimer(sessionId);
                        break;
                    case 'end':
                        result = await this.gameSessionService.endGame(sessionId);
                        break;
                    case 'cancel':
                        result = await this.gameSessionService.cancelGame(sessionId, reason);
                        break;
                }
                results.push({ sessionId, success: true, result });
            }
            catch (error) {
                results.push({ sessionId, success: false, error: error.message });
            }
        }
        return this.ok(results);
    }
};
exports.AdminGameSessionController = AdminGameSessionController;
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", session_2.GameSessionService)
], AdminGameSessionController.prototype, "gameSessionService", void 0);
__decorate([
    (0, core_1.Post)('/startGame'),
    __param(0, (0, core_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [session_3.StartGameDTO]),
    __metadata("design:returntype", Promise)
], AdminGameSessionController.prototype, "startGame", null);
__decorate([
    (0, core_1.Post)('/startTimer'),
    __param(0, (0, core_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AdminGameSessionController.prototype, "startGameTimer", null);
__decorate([
    (0, core_1.Post)('/endGame'),
    __param(0, (0, core_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AdminGameSessionController.prototype, "endGame", null);
__decorate([
    (0, core_1.Post)('/cancelGame'),
    __param(0, (0, core_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AdminGameSessionController.prototype, "cancelGame", null);
__decorate([
    (0, core_1.Get)('/active'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminGameSessionController.prototype, "getActiveGames", null);
__decorate([
    (0, core_1.Get)('/todayStats'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminGameSessionController.prototype, "getTodayStats", null);
__decorate([
    (0, core_1.Post)('/create'),
    __param(0, (0, core_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [session_3.GameSessionCreateDTO]),
    __metadata("design:returntype", Promise)
], AdminGameSessionController.prototype, "createSession", null);
__decorate([
    (0, core_1.Post)('/updateSession'),
    __param(0, (0, core_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [session_3.GameSessionUpdateDTO]),
    __metadata("design:returntype", Promise)
], AdminGameSessionController.prototype, "updateSession", null);
__decorate([
    (0, core_1.Get)('/detail'),
    __param(0, (0, core_1.Query)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AdminGameSessionController.prototype, "getDetail", null);
__decorate([
    (0, core_1.Post)('/batchOperation'),
    __param(0, (0, core_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AdminGameSessionController.prototype, "batchOperation", null);
exports.AdminGameSessionController = AdminGameSessionController = __decorate([
    (0, core_2.CoolController)({
        api: ['add', 'delete', 'update', 'info', 'list', 'page'],
        entity: session_1.GameSessionEntity,
        service: session_2.GameSessionService,
        pageQueryOp: {
            select: ['a.*', 'b.name as themeName'],
            join: [
                {
                    entity: theme_1.GameThemeEntity,
                    alias: 'b',
                    condition: 'a.themeId = b.id',
                    type: 'leftJoin',
                },
            ],
        },
    })
], AdminGameSessionController);
//# sourceMappingURL=data:application/json;base64,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