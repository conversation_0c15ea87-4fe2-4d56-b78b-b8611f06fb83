<template>
	<div class="session-management-test">
		<el-card header="智能场次管理测试">
			<div class="test-section">
				<h3>1. 场次网格组件测试</h3>
				<SessionGrid
					:theme-options="themeOptions"
					v-model="selectedTimeSlot"
					@slot-selected="onSlotSelected"
				/>
			</div>

			<div class="test-section">
				<h3>2. 智能时间选择器测试</h3>
				<SmartTimePicker
					:theme-options="themeOptions"
					v-model="selectedTimeSlot"
					@theme-change="onThemeChange"
				/>
			</div>

			<div class="test-section">
				<h3>3. 选中的时间段</h3>
				<el-descriptions border>
					<el-descriptions-item label="开始时间">
						{{ selectedTimeSlot?.startTime ? formatTime(selectedTimeSlot.startTime) : '未选择' }}
					</el-descriptions-item>
					<el-descriptions-item label="结束时间">
						{{ selectedTimeSlot?.endTime ? formatTime(selectedTimeSlot.endTime) : '未选择' }}
					</el-descriptions-item>
				</el-descriptions>
			</div>

			<div class="test-section">
				<h3>4. 服务测试</h3>
				<el-row :gutter="20">
					<el-col :span="8">
						<el-button @click="testSessionService" type="primary">测试场次服务</el-button>
					</el-col>
					<el-col :span="8">
						<el-button @click="testSettingService" type="success">测试设置服务</el-button>
					</el-col>
					<el-col :span="8">
						<el-button @click="testReservationService" type="warning">测试预约服务</el-button>
					</el-col>
				</el-row>
			</div>

			<div class="test-section">
				<h3>5. 测试结果</h3>
				<el-input
					v-model="testResults"
					type="textarea"
					:rows="10"
					placeholder="测试结果将显示在这里..."
					readonly
				/>
			</div>
		</el-card>
	</div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useCool } from '/@/cool';
import { ElMessage } from 'element-plus';
import SessionGrid from '../../components/SessionGrid.vue';
import SmartTimePicker from '../../components/SmartTimePicker.vue';

const { service } = useCool();

// 响应式数据
const themeOptions = ref<any[]>([]);
const selectedTimeSlot = ref<{ startTime: string; endTime: string }>();
const testResults = ref('');

// 方法
const loadThemeOptions = async () => {
	try {
		const res = await service.game.theme.list();
		themeOptions.value = res || [];
		addTestResult('✅ 主题选项加载成功', res);
	} catch (error) {
		addTestResult('❌ 主题选项加载失败', error);
	}
};

const onSlotSelected = (slot: any) => {
	addTestResult('🎯 场次选择', slot);
};

const onThemeChange = (themeId: number) => {
	addTestResult('🎨 主题变更', { themeId });
};

const testSessionService = async () => {
	try {
		addTestResult('🧪 开始测试场次服务...');
		
		// 测试获取场次计划
		const planResult = await service.game.session.getSessionPlan({
			date: new Date().toISOString().split('T')[0],
			themeId: themeOptions.value[0]?.id || 1
		});
		addTestResult('✅ 获取场次计划', planResult);
		
		// 测试检查时间冲突
		const conflictResult = await service.game.session.checkTimeConflict({
			themeId: themeOptions.value[0]?.id || 1,
			startTime: new Date().toISOString(),
			endTime: new Date(Date.now() + 90 * 60 * 1000).toISOString()
		});
		addTestResult('✅ 检查时间冲突', conflictResult);
		
		// 测试获取可用时段
		const slotsResult = await service.game.session.getAvailableTimeSlots({
			themeId: themeOptions.value[0]?.id || 1,
			date: new Date().toISOString().split('T')[0],
			duration: 90
		});
		addTestResult('✅ 获取可用时段', slotsResult);
		
	} catch (error) {
		addTestResult('❌ 场次服务测试失败', error);
	}
};

const testSettingService = async () => {
	try {
		addTestResult('🧪 开始测试设置服务...');
		
		// 测试获取营业时间
		const businessHours = await service.game.sessionSetting.getBusinessHours();
		addTestResult('✅ 获取营业时间', businessHours);
		
		// 测试获取休息日
		const restDays = await service.game.sessionSetting.getRestDays();
		addTestResult('✅ 获取休息日', restDays);
		
		// 测试获取场次参数
		const sessionParams = await service.game.sessionSetting.getSessionParams();
		addTestResult('✅ 获取场次参数', sessionParams);
		
		// 测试获取主题参数
		if (themeOptions.value.length > 0) {
			const themeParams = await service.game.sessionSetting.getThemeSessionParams(themeOptions.value[0].id);
			addTestResult('✅ 获取主题参数', themeParams);
		}
		
	} catch (error) {
		addTestResult('❌ 设置服务测试失败', error);
	}
};

const testReservationService = async () => {
	try {
		addTestResult('🧪 开始测试预约服务...');
		
		// 测试获取即将开始的预约
		const upcomingReservations = await service.game.reservation.getUpcomingReservations();
		addTestResult('✅ 获取即将开始的预约', upcomingReservations);
		
		// 测试分页查询
		const pageResult = await service.game.reservation.page({
			page: 1,
			size: 10
		});
		addTestResult('✅ 分页查询预约', pageResult);
		
	} catch (error) {
		addTestResult('❌ 预约服务测试失败', error);
	}
};

const addTestResult = (title: string, data?: any) => {
	const timestamp = new Date().toLocaleTimeString();
	const result = `[${timestamp}] ${title}\n${data ? JSON.stringify(data, null, 2) : ''}\n\n`;
	testResults.value += result;
};

const formatTime = (time: string) => {
	return new Date(time).toLocaleString('zh-CN');
};

// 初始化
onMounted(() => {
	loadThemeOptions();
	addTestResult('🚀 智能场次管理测试页面已加载');
});
</script>

<style lang="scss" scoped>
.session-management-test {
	padding: 20px;
	
	.test-section {
		margin-bottom: 30px;
		
		h3 {
			margin-bottom: 16px;
			color: #303133;
			border-bottom: 2px solid #409eff;
			padding-bottom: 8px;
		}
	}
}

:deep(.el-card__header) {
	background: #f8f9fa;
	font-weight: bold;
	font-size: 16px;
}
</style>
