# 游戏预约管理模块 - 智能场次管理系统

## 概述

本模块实现了一个完整的游戏预约管理系统，包含智能场次管理功能。系统支持自动场次计划生成、智能时间选择、冲突检测和可视化场次网格等功能。

## 主要功能

### 1. 智能场次管理
- **自动场次计划生成**: 根据营业时间、主题参数自动生成场次计划
- **智能时间选择**: 提供网格视图和手动选择两种模式
- **冲突检测**: 实时检测时间冲突并提供替代方案
- **可视化场次网格**: 直观显示场次状态和可用性

### 2. 基础设置管理
- **营业时间设置**: 支持每日不同营业时间配置
- **休息日管理**: 固定休息日和临时休息日设置
- **场次参数配置**: 默认游戏时长、间隔时间等参数
- **主题特定参数**: 为不同主题设置专属参数

### 3. 预约管理
- **预约CRUD操作**: 完整的预约增删改查功能
- **状态管理**: 待确认、已确认、已完成、已取消状态流转
- **批量操作**: 批量状态更新、导出等功能
- **会员集成**: 支持会员搜索和关联

## 技术架构

### 服务层 (Services)
- `GameSessionService`: 场次管理核心服务
- `GameSessionSettingService`: 场次设置服务
- `GameReservationService`: 预约管理服务
- `GameThemeService`: 主题管理服务
- `GameMemberService`: 会员管理服务

### 组件层 (Components)
- `SessionGrid`: 场次网格组件
- `SmartTimePicker`: 智能时间选择器组件

### 页面层 (Views)
- `reservation/index.vue`: 预约管理主页面
- `settings/index.vue`: 基础设置页面
- `test/session-management.vue`: 功能测试页面

## 核心功能实现

### 1. 场次网格 (SessionGrid)

```vue
<SessionGrid
  :theme-options="themeOptions"
  v-model="selectedTimeSlot"
  @slot-selected="onSlotSelected"
/>
```

**功能特性:**
- 可视化时间段网格
- 实时状态显示 (可预约/已预约/进行中/维护中/已关闭)
- 悬浮提示显示详细信息
- 点击选择时间段
- 自动场次计划生成

### 2. 智能时间选择器 (SmartTimePicker)

```vue
<SmartTimePicker
  :theme-options="themeOptions"
  v-model="selectedTimeSlot"
  @theme-change="onThemeChange"
/>
```

**功能特性:**
- 网格模式和手动模式切换
- 实时冲突检测
- 推荐可用时段
- 营业时间限制
- 主题参数自动应用

### 3. 场次计划生成

```typescript
// 生成场次计划
await service.game.session.generateSessionPlan({
  themeId: selectedThemeId,
  date: formattedDate,
  businessHours: { start: '09:00', end: '22:00' },
  gameDuration: 90,
  intervalTime: 15
});
```

### 4. 冲突检测

```typescript
// 检查时间冲突
const conflictResult = await service.game.session.checkTimeConflict({
  themeId: selectedThemeId,
  startTime: startTime,
  endTime: endTime
});
```

## 数据流程

### 1. 场次计划生成流程
1. 获取营业时间设置
2. 获取主题参数 (游戏时长、间隔时间)
3. 根据参数自动生成时间段
4. 检查已有预约避免冲突
5. 生成可用场次列表

### 2. 智能预约流程
1. 用户选择主题
2. 系统加载主题参数
3. 显示可用时间段网格
4. 用户选择时间段或手动输入
5. 实时冲突检测
6. 提供替代方案 (如有冲突)
7. 确认预约

### 3. 设置管理流程
1. 营业时间配置 (按星期设置)
2. 休息日管理 (固定+临时)
3. 默认参数设置
4. 主题特定参数配置
5. 实时生效应用

## API 接口

### 场次管理接口
- `GET /admin/game/session/plan` - 获取场次计划
- `POST /admin/game/session/generate` - 生成场次计划
- `POST /admin/game/session/conflict` - 检查时间冲突
- `GET /admin/game/session/slots` - 获取可用时段

### 设置管理接口
- `GET /admin/game/setting/business-hours` - 获取营业时间
- `POST /admin/game/setting/business-hours` - 设置营业时间
- `GET /admin/game/setting/rest-days` - 获取休息日
- `POST /admin/game/setting/rest-days` - 设置休息日
- `GET /admin/game/setting/session-params` - 获取场次参数
- `POST /admin/game/setting/session-params` - 设置场次参数

### 预约管理接口
- `GET /admin/game/reservation/page` - 分页查询预约
- `POST /admin/game/reservation/add` - 新增预约
- `POST /admin/game/reservation/update` - 更新预约
- `POST /admin/game/reservation/confirm` - 确认预约
- `POST /admin/game/reservation/cancel` - 取消预约
- `POST /admin/game/reservation/batchUpdateStatus` - 批量更新状态

## 使用说明

### 1. 基础设置配置
1. 进入 "基础设置" 页面
2. 配置营业时间 (每日开始/结束时间)
3. 设置休息日 (固定休息日 + 临时休息日)
4. 配置默认场次参数 (游戏时长、间隔时间等)
5. 为特定主题设置专属参数

### 2. 智能预约操作
1. 进入 "预约管理" 页面
2. 点击 "新增" 按钮
3. 在智能时间选择器中:
   - 选择 "场次网格" 模式查看可用时段
   - 或选择 "手动选择" 模式自定义时间
4. 选择主题 (自动应用主题参数)
5. 填写客户信息
6. 系统自动检测冲突并提供建议
7. 确认预约

### 3. 场次管理操作
1. 在场次网格中查看当日场次状态
2. 点击 "生成场次计划" 自动创建时间段
3. 通过颜色区分不同状态:
   - 绿色: 可预约
   - 灰色: 已预约
   - 蓝色: 进行中
   - 橙色: 维护中
   - 红色: 已关闭

## 测试功能

访问 "场次管理测试" 页面可以:
- 测试场次网格组件
- 测试智能时间选择器
- 验证各项服务接口
- 查看实时测试结果

## 扩展功能

系统设计支持以下扩展:
- 多房间管理
- 价格策略配置
- 优惠券集成
- 会员等级权益
- 数据统计分析
- 移动端适配

## 注意事项

1. **时间格式**: 统一使用 ISO 8601 格式
2. **时区处理**: 默认使用本地时区
3. **冲突检测**: 考虑游戏时长和清理时间
4. **数据一致性**: 场次状态实时同步
5. **性能优化**: 大量数据时使用分页加载
