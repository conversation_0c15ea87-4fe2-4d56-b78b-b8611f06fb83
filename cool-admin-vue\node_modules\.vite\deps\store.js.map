{"version": 3, "sources": ["../../store/src/util.js", "../../store/src/store-engine.js", "../../store/storages/localStorage.js", "../../store/storages/oldFF-globalStorage.js", "../../store/storages/oldIE-userDataStorage.js", "../../store/storages/cookieStorage.js", "../../store/storages/sessionStorage.js", "../../store/storages/memoryStorage.js", "../../store/storages/all.js", "../../store/plugins/lib/json2.js", "../../store/plugins/json2.js", "../../store/dist/store.legacy.js"], "sourcesContent": ["var assign = make_assign()\nvar create = make_create()\nvar trim = make_trim()\nvar Global = (typeof window !== 'undefined' ? window : global)\n\nmodule.exports = {\n\tassign: assign,\n\tcreate: create,\n\ttrim: trim,\n\tbind: bind,\n\tslice: slice,\n\teach: each,\n\tmap: map,\n\tpluck: pluck,\n\tisList: isList,\n\tisFunction: isFunction,\n\tisObject: isObject,\n\tGlobal: Global\n}\n\nfunction make_assign() {\n\tif (Object.assign) {\n\t\treturn Object.assign\n\t} else {\n\t\treturn function shimAssign(obj, props1, props2, etc) {\n\t\t\tfor (var i = 1; i < arguments.length; i++) {\n\t\t\t\teach(Object(arguments[i]), function(val, key) {\n\t\t\t\t\tobj[key] = val\n\t\t\t\t})\n\t\t\t}\t\t\t\n\t\t\treturn obj\n\t\t}\n\t}\n}\n\nfunction make_create() {\n\tif (Object.create) {\n\t\treturn function create(obj, assignProps1, assignProps2, etc) {\n\t\t\tvar assignArgsList = slice(arguments, 1)\n\t\t\treturn assign.apply(this, [Object.create(obj)].concat(assignArgsList))\n\t\t}\n\t} else {\n\t\tfunction F() {} // eslint-disable-line no-inner-declarations\n\t\treturn function create(obj, assignProps1, assignProps2, etc) {\n\t\t\tvar assignArgsList = slice(arguments, 1)\n\t\t\tF.prototype = obj\n\t\t\treturn assign.apply(this, [new F()].concat(assignArgsList))\n\t\t}\n\t}\n}\n\nfunction make_trim() {\n\tif (String.prototype.trim) {\n\t\treturn function trim(str) {\n\t\t\treturn String.prototype.trim.call(str)\n\t\t}\n\t} else {\n\t\treturn function trim(str) {\n\t\t\treturn str.replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g, '')\n\t\t}\n\t}\n}\n\nfunction bind(obj, fn) {\n\treturn function() {\n\t\treturn fn.apply(obj, Array.prototype.slice.call(arguments, 0))\n\t}\n}\n\nfunction slice(arr, index) {\n\treturn Array.prototype.slice.call(arr, index || 0)\n}\n\nfunction each(obj, fn) {\n\tpluck(obj, function(val, key) {\n\t\tfn(val, key)\n\t\treturn false\n\t})\n}\n\nfunction map(obj, fn) {\n\tvar res = (isList(obj) ? [] : {})\n\tpluck(obj, function(v, k) {\n\t\tres[k] = fn(v, k)\n\t\treturn false\n\t})\n\treturn res\n}\n\nfunction pluck(obj, fn) {\n\tif (isList(obj)) {\n\t\tfor (var i=0; i<obj.length; i++) {\n\t\t\tif (fn(obj[i], i)) {\n\t\t\t\treturn obj[i]\n\t\t\t}\n\t\t}\n\t} else {\n\t\tfor (var key in obj) {\n\t\t\tif (obj.hasOwnProperty(key)) {\n\t\t\t\tif (fn(obj[key], key)) {\n\t\t\t\t\treturn obj[key]\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\nfunction isList(val) {\n\treturn (val != null && typeof val != 'function' && typeof val.length == 'number')\n}\n\nfunction isFunction(val) {\n\treturn val && {}.toString.call(val) === '[object Function]'\n}\n\nfunction isObject(val) {\n\treturn val && {}.toString.call(val) === '[object Object]'\n}\n", "var util = require('./util')\nvar slice = util.slice\nvar pluck = util.pluck\nvar each = util.each\nvar bind = util.bind\nvar create = util.create\nvar isList = util.isList\nvar isFunction = util.isFunction\nvar isObject = util.isObject\n\nmodule.exports = {\n\tcreateStore: createStore\n}\n\nvar storeAPI = {\n\tversion: '2.0.12',\n\tenabled: false,\n\t\n\t// get returns the value of the given key. If that value\n\t// is undefined, it returns optionalDefaultValue instead.\n\tget: function(key, optionalDefaultValue) {\n\t\tvar data = this.storage.read(this._namespacePrefix + key)\n\t\treturn this._deserialize(data, optionalDefaultValue)\n\t},\n\n\t// set will store the given value at key and returns value.\n\t// Calling set with value === undefined is equivalent to calling remove.\n\tset: function(key, value) {\n\t\tif (value === undefined) {\n\t\t\treturn this.remove(key)\n\t\t}\n\t\tthis.storage.write(this._namespacePrefix + key, this._serialize(value))\n\t\treturn value\n\t},\n\n\t// remove deletes the key and value stored at the given key.\n\tremove: function(key) {\n\t\tthis.storage.remove(this._namespacePrefix + key)\n\t},\n\n\t// each will call the given callback once for each key-value pair\n\t// in this store.\n\teach: function(callback) {\n\t\tvar self = this\n\t\tthis.storage.each(function(val, namespacedKey) {\n\t\t\tcallback.call(self, self._deserialize(val), (namespacedKey || '').replace(self._namespaceRegexp, ''))\n\t\t})\n\t},\n\n\t// clearAll will remove all the stored key-value pairs in this store.\n\tclearAll: function() {\n\t\tthis.storage.clearAll()\n\t},\n\n\t// additional functionality that can't live in plugins\n\t// ---------------------------------------------------\n\n\t// hasNamespace returns true if this store instance has the given namespace.\n\thasNamespace: function(namespace) {\n\t\treturn (this._namespacePrefix == '__storejs_'+namespace+'_')\n\t},\n\n\t// createStore creates a store.js instance with the first\n\t// functioning storage in the list of storage candidates,\n\t// and applies the the given mixins to the instance.\n\tcreateStore: function() {\n\t\treturn createStore.apply(this, arguments)\n\t},\n\t\n\taddPlugin: function(plugin) {\n\t\tthis._addPlugin(plugin)\n\t},\n\t\n\tnamespace: function(namespace) {\n\t\treturn createStore(this.storage, this.plugins, namespace)\n\t}\n}\n\nfunction _warn() {\n\tvar _console = (typeof console == 'undefined' ? null : console)\n\tif (!_console) { return }\n\tvar fn = (_console.warn ? _console.warn : _console.log)\n\tfn.apply(_console, arguments)\n}\n\nfunction createStore(storages, plugins, namespace) {\n\tif (!namespace) {\n\t\tnamespace = ''\n\t}\n\tif (storages && !isList(storages)) {\n\t\tstorages = [storages]\n\t}\n\tif (plugins && !isList(plugins)) {\n\t\tplugins = [plugins]\n\t}\n\n\tvar namespacePrefix = (namespace ? '__storejs_'+namespace+'_' : '')\n\tvar namespaceRegexp = (namespace ? new RegExp('^'+namespacePrefix) : null)\n\tvar legalNamespaces = /^[a-zA-Z0-9_\\-]*$/ // alpha-numeric + underscore and dash\n\tif (!legalNamespaces.test(namespace)) {\n\t\tthrow new Error('store.js namespaces can only have alphanumerics + underscores and dashes')\n\t}\n\t\n\tvar _privateStoreProps = {\n\t\t_namespacePrefix: namespacePrefix,\n\t\t_namespaceRegexp: namespaceRegexp,\n\n\t\t_testStorage: function(storage) {\n\t\t\ttry {\n\t\t\t\tvar testStr = '__storejs__test__'\n\t\t\t\tstorage.write(testStr, testStr)\n\t\t\t\tvar ok = (storage.read(testStr) === testStr)\n\t\t\t\tstorage.remove(testStr)\n\t\t\t\treturn ok\n\t\t\t} catch(e) {\n\t\t\t\treturn false\n\t\t\t}\n\t\t},\n\n\t\t_assignPluginFnProp: function(pluginFnProp, propName) {\n\t\t\tvar oldFn = this[propName]\n\t\t\tthis[propName] = function pluginFn() {\n\t\t\t\tvar args = slice(arguments, 0)\n\t\t\t\tvar self = this\n\n\t\t\t\t// super_fn calls the old function which was overwritten by\n\t\t\t\t// this mixin.\n\t\t\t\tfunction super_fn() {\n\t\t\t\t\tif (!oldFn) { return }\n\t\t\t\t\teach(arguments, function(arg, i) {\n\t\t\t\t\t\targs[i] = arg\n\t\t\t\t\t})\n\t\t\t\t\treturn oldFn.apply(self, args)\n\t\t\t\t}\n\n\t\t\t\t// Give mixing function access to super_fn by prefixing all mixin function\n\t\t\t\t// arguments with super_fn.\n\t\t\t\tvar newFnArgs = [super_fn].concat(args)\n\n\t\t\t\treturn pluginFnProp.apply(self, newFnArgs)\n\t\t\t}\n\t\t},\n\n\t\t_serialize: function(obj) {\n\t\t\treturn JSON.stringify(obj)\n\t\t},\n\n\t\t_deserialize: function(strVal, defaultVal) {\n\t\t\tif (!strVal) { return defaultVal }\n\t\t\t// It is possible that a raw string value has been previously stored\n\t\t\t// in a storage without using store.js, meaning it will be a raw\n\t\t\t// string value instead of a JSON serialized string. By defaulting\n\t\t\t// to the raw string value in case of a JSON parse error, we allow\n\t\t\t// for past stored values to be forwards-compatible with store.js\n\t\t\tvar val = ''\n\t\t\ttry { val = JSON.parse(strVal) }\n\t\t\tcatch(e) { val = strVal }\n\n\t\t\treturn (val !== undefined ? val : defaultVal)\n\t\t},\n\t\t\n\t\t_addStorage: function(storage) {\n\t\t\tif (this.enabled) { return }\n\t\t\tif (this._testStorage(storage)) {\n\t\t\t\tthis.storage = storage\n\t\t\t\tthis.enabled = true\n\t\t\t}\n\t\t},\n\n\t\t_addPlugin: function(plugin) {\n\t\t\tvar self = this\n\n\t\t\t// If the plugin is an array, then add all plugins in the array.\n\t\t\t// This allows for a plugin to depend on other plugins.\n\t\t\tif (isList(plugin)) {\n\t\t\t\teach(plugin, function(plugin) {\n\t\t\t\t\tself._addPlugin(plugin)\n\t\t\t\t})\n\t\t\t\treturn\n\t\t\t}\n\n\t\t\t// Keep track of all plugins we've seen so far, so that we\n\t\t\t// don't add any of them twice.\n\t\t\tvar seenPlugin = pluck(this.plugins, function(seenPlugin) {\n\t\t\t\treturn (plugin === seenPlugin)\n\t\t\t})\n\t\t\tif (seenPlugin) {\n\t\t\t\treturn\n\t\t\t}\n\t\t\tthis.plugins.push(plugin)\n\n\t\t\t// Check that the plugin is properly formed\n\t\t\tif (!isFunction(plugin)) {\n\t\t\t\tthrow new Error('Plugins must be function values that return objects')\n\t\t\t}\n\n\t\t\tvar pluginProperties = plugin.call(this)\n\t\t\tif (!isObject(pluginProperties)) {\n\t\t\t\tthrow new Error('Plugins must return an object of function properties')\n\t\t\t}\n\n\t\t\t// Add the plugin function properties to this store instance.\n\t\t\teach(pluginProperties, function(pluginFnProp, propName) {\n\t\t\t\tif (!isFunction(pluginFnProp)) {\n\t\t\t\t\tthrow new Error('Bad plugin property: '+propName+' from plugin '+plugin.name+'. Plugins should only return functions.')\n\t\t\t\t}\n\t\t\t\tself._assignPluginFnProp(pluginFnProp, propName)\n\t\t\t})\n\t\t},\n\t\t\n\t\t// Put deprecated properties in the private API, so as to not expose it to accidential\n\t\t// discovery through inspection of the store object.\n\t\t\n\t\t// Deprecated: addStorage\n\t\taddStorage: function(storage) {\n\t\t\t_warn('store.addStorage(storage) is deprecated. Use createStore([storages])')\n\t\t\tthis._addStorage(storage)\n\t\t}\n\t}\n\n\tvar store = create(_privateStoreProps, storeAPI, {\n\t\tplugins: []\n\t})\n\tstore.raw = {}\n\teach(store, function(prop, propName) {\n\t\tif (isFunction(prop)) {\n\t\t\tstore.raw[propName] = bind(store, prop)\t\t\t\n\t\t}\n\t})\n\teach(storages, function(storage) {\n\t\tstore._addStorage(storage)\n\t})\n\teach(plugins, function(plugin) {\n\t\tstore._addPlugin(plugin)\n\t})\n\treturn store\n}\n", "var util = require('../src/util')\nvar Global = util.Global\n\nmodule.exports = {\n\tname: 'localStorage',\n\tread: read,\n\twrite: write,\n\teach: each,\n\tremove: remove,\n\tclearAll: clearAll,\n}\n\nfunction localStorage() {\n\treturn Global.localStorage\n}\n\nfunction read(key) {\n\treturn localStorage().getItem(key)\n}\n\nfunction write(key, data) {\n\treturn localStorage().setItem(key, data)\n}\n\nfunction each(fn) {\n\tfor (var i = localStorage().length - 1; i >= 0; i--) {\n\t\tvar key = localStorage().key(i)\n\t\tfn(read(key), key)\n\t}\n}\n\nfunction remove(key) {\n\treturn localStorage().removeItem(key)\n}\n\nfunction clearAll() {\n\treturn localStorage().clear()\n}\n", "// oldFF-globalStorage provides storage for Firefox\n// versions 6 and 7, where no localStorage, etc\n// is available.\n\nvar util = require('../src/util')\nvar Global = util.Global\n\nmodule.exports = {\n\tname: 'oldFF-globalStorage',\n\tread: read,\n\twrite: write,\n\teach: each,\n\tremove: remove,\n\tclearAll: clearAll,\n}\n\nvar globalStorage = Global.globalStorage\n\nfunction read(key) {\n\treturn globalStorage[key]\n}\n\nfunction write(key, data) {\n\tglobalStorage[key] = data\n}\n\nfunction each(fn) {\n\tfor (var i = globalStorage.length - 1; i >= 0; i--) {\n\t\tvar key = globalStorage.key(i)\n\t\tfn(globalStorage[key], key)\n\t}\n}\n\nfunction remove(key) {\n\treturn globalStorage.removeItem(key)\n}\n\nfunction clearAll() {\n\teach(function(key, _) {\n\t\tdelete globalStorage[key]\n\t})\n}\n", "// oldIE-userDataStorage provides storage for Internet Explorer\n// versions 6 and 7, where no localStorage, sessionStorage, etc\n// is available.\n\nvar util = require('../src/util')\nvar Global = util.Global\n\nmodule.exports = {\n\tname: 'oldIE-userDataStorage',\n\twrite: write,\n\tread: read,\n\teach: each,\n\tremove: remove,\n\tclearAll: clearAll,\n}\n\nvar storageName = 'storejs'\nvar doc = Global.document\nvar _withStorageEl = _makeIEStorageElFunction()\nvar disable = (Global.navigator ? Global.navigator.userAgent : '').match(/ (MSIE 8|MSIE 9|MSIE 10)\\./) // MSIE 9.x, MSIE 10.x\n\nfunction write(unfixedKey, data) {\n\tif (disable) { return }\n\tvar fixedKey = fixKey(unfixedKey)\n\t_withStorageEl(function(storageEl) {\n\t\tstorageEl.setAttribute(fixedKey, data)\n\t\tstorageEl.save(storageName)\n\t})\n}\n\nfunction read(unfixedKey) {\n\tif (disable) { return }\n\tvar fixedKey = fixKey(unfixedKey)\n\tvar res = null\n\t_withStorageEl(function(storageEl) {\n\t\tres = storageEl.getAttribute(fixedKey)\n\t})\n\treturn res\n}\n\nfunction each(callback) {\n\t_withStorageEl(function(storageEl) {\n\t\tvar attributes = storageEl.XMLDocument.documentElement.attributes\n\t\tfor (var i=attributes.length-1; i>=0; i--) {\n\t\t\tvar attr = attributes[i]\n\t\t\tcallback(storageEl.getAttribute(attr.name), attr.name)\n\t\t}\n\t})\n}\n\nfunction remove(unfixedKey) {\n\tvar fixedKey = fixKey(unfixedKey)\n\t_withStorageEl(function(storageEl) {\n\t\tstorageEl.removeAttribute(fixedKey)\n\t\tstorageEl.save(storageName)\n\t})\n}\n\nfunction clearAll() {\n\t_withStorageEl(function(storageEl) {\n\t\tvar attributes = storageEl.XMLDocument.documentElement.attributes\n\t\tstorageEl.load(storageName)\n\t\tfor (var i=attributes.length-1; i>=0; i--) {\n\t\t\tstorageEl.removeAttribute(attributes[i].name)\n\t\t}\n\t\tstorageEl.save(storageName)\n\t})\n}\n\n// Helpers\n//////////\n\n// In IE7, keys cannot start with a digit or contain certain chars.\n// See https://github.com/marcuswestin/store.js/issues/40\n// See https://github.com/marcuswestin/store.js/issues/83\nvar forbiddenCharsRegex = new RegExp(\"[!\\\"#$%&'()*+,/\\\\\\\\:;<=>?@[\\\\]^`{|}~]\", \"g\")\nfunction fixKey(key) {\n\treturn key.replace(/^\\d/, '___$&').replace(forbiddenCharsRegex, '___')\n}\n\nfunction _makeIEStorageElFunction() {\n\tif (!doc || !doc.documentElement || !doc.documentElement.addBehavior) {\n\t\treturn null\n\t}\n\tvar scriptTag = 'script',\n\t\tstorageOwner,\n\t\tstorageContainer,\n\t\tstorageEl\n\n\t// Since #userData storage applies only to specific paths, we need to\n\t// somehow link our data to a specific path.  We choose /favicon.ico\n\t// as a pretty safe option, since all browsers already make a request to\n\t// this URL anyway and being a 404 will not hurt us here.  We wrap an\n\t// iframe pointing to the favicon in an ActiveXObject(htmlfile) object\n\t// (see: http://msdn.microsoft.com/en-us/library/aa752574(v=VS.85).aspx)\n\t// since the iframe access rules appear to allow direct access and\n\t// manipulation of the document element, even for a 404 page.  This\n\t// document can be used instead of the current document (which would\n\t// have been limited to the current path) to perform #userData storage.\n\ttry {\n\t\t/* global ActiveXObject */\n\t\tstorageContainer = new ActiveXObject('htmlfile')\n\t\tstorageContainer.open()\n\t\tstorageContainer.write('<'+scriptTag+'>document.w=window</'+scriptTag+'><iframe src=\"/favicon.ico\"></iframe>')\n\t\tstorageContainer.close()\n\t\tstorageOwner = storageContainer.w.frames[0].document\n\t\tstorageEl = storageOwner.createElement('div')\n\t} catch(e) {\n\t\t// somehow ActiveXObject instantiation failed (perhaps some special\n\t\t// security settings or otherwse), fall back to per-path storage\n\t\tstorageEl = doc.createElement('div')\n\t\tstorageOwner = doc.body\n\t}\n\n\treturn function(storeFunction) {\n\t\tvar args = [].slice.call(arguments, 0)\n\t\targs.unshift(storageEl)\n\t\t// See http://msdn.microsoft.com/en-us/library/ms531081(v=VS.85).aspx\n\t\t// and http://msdn.microsoft.com/en-us/library/ms531424(v=VS.85).aspx\n\t\tstorageOwner.appendChild(storageEl)\n\t\tstorageEl.addBehavior('#default#userData')\n\t\tstorageEl.load(storageName)\n\t\tstoreFunction.apply(this, args)\n\t\tstorageOwner.removeChild(storageEl)\n\t\treturn\n\t}\n}\n", "// cookieStorage is useful Safari private browser mode, where localStorage\n// doesn't work but cookies do. This implementation is adopted from\n// https://developer.mozilla.org/en-US/docs/Web/API/Storage/LocalStorage\n\nvar util = require('../src/util')\nvar Global = util.Global\nvar trim = util.trim\n\nmodule.exports = {\n\tname: 'cookieStorage',\n\tread: read,\n\twrite: write,\n\teach: each,\n\tremove: remove,\n\tclearAll: clearAll,\n}\n\nvar doc = Global.document\n\nfunction read(key) {\n\tif (!key || !_has(key)) { return null }\n\tvar regexpStr = \"(?:^|.*;\\\\s*)\" +\n\t\tescape(key).replace(/[\\-\\.\\+\\*]/g, \"\\\\$&\") +\n\t\t\"\\\\s*\\\\=\\\\s*((?:[^;](?!;))*[^;]?).*\"\n\treturn unescape(doc.cookie.replace(new RegExp(regexpStr), \"$1\"))\n}\n\nfunction each(callback) {\n\tvar cookies = doc.cookie.split(/; ?/g)\n\tfor (var i = cookies.length - 1; i >= 0; i--) {\n\t\tif (!trim(cookies[i])) {\n\t\t\tcontinue\n\t\t}\n\t\tvar kvp = cookies[i].split('=')\n\t\tvar key = unescape(kvp[0])\n\t\tvar val = unescape(kvp[1])\n\t\tcallback(val, key)\n\t}\n}\n\nfunction write(key, data) {\n\tif(!key) { return }\n\tdoc.cookie = escape(key) + \"=\" + escape(data) + \"; expires=Tue, 19 Jan 2038 03:14:07 GMT; path=/\"\n}\n\nfunction remove(key) {\n\tif (!key || !_has(key)) {\n\t\treturn\n\t}\n\tdoc.cookie = escape(key) + \"=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/\"\n}\n\nfunction clearAll() {\n\teach(function(_, key) {\n\t\tremove(key)\n\t})\n}\n\nfunction _has(key) {\n\treturn (new RegExp(\"(?:^|;\\\\s*)\" + escape(key).replace(/[\\-\\.\\+\\*]/g, \"\\\\$&\") + \"\\\\s*\\\\=\")).test(doc.cookie)\n}\n", "var util = require('../src/util')\nvar Global = util.Global\n\nmodule.exports = {\n\tname: 'sessionStorage',\n\tread: read,\n\twrite: write,\n\teach: each,\n\tremove: remove,\n\tclearAll: clearAll\n}\n\nfunction sessionStorage() {\n\treturn Global.sessionStorage\n}\n\nfunction read(key) {\n\treturn sessionStorage().getItem(key)\n}\n\nfunction write(key, data) {\n\treturn sessionStorage().setItem(key, data)\n}\n\nfunction each(fn) {\n\tfor (var i = sessionStorage().length - 1; i >= 0; i--) {\n\t\tvar key = sessionStorage().key(i)\n\t\tfn(read(key), key)\n\t}\n}\n\nfunction remove(key) {\n\treturn sessionStorage().removeItem(key)\n}\n\nfunction clearAll() {\n\treturn sessionStorage().clear()\n}\n", "// memoryStorage is a useful last fallback to ensure that the store\n// is functions (meaning store.get(), store.set(), etc will all function).\n// However, stored values will not persist when the browser navigates to\n// a new page or reloads the current page.\n\nmodule.exports = {\n\tname: 'memoryStorage',\n\tread: read,\n\twrite: write,\n\teach: each,\n\tremove: remove,\n\tclearAll: clearAll,\n}\n\nvar memoryStorage = {}\n\nfunction read(key) {\n\treturn memoryStorage[key]\n}\n\nfunction write(key, data) {\n\tmemoryStorage[key] = data\n}\n\nfunction each(callback) {\n\tfor (var key in memoryStorage) {\n\t\tif (memoryStorage.hasOwnProperty(key)) {\n\t\t\tcallback(memoryStorage[key], key)\n\t\t}\n\t}\n}\n\nfunction remove(key) {\n\tdelete memoryStorage[key]\n}\n\nfunction clearAll(key) {\n\tmemoryStorage = {}\n}\n", "module.exports = [\n\t// Listed in order of usage preference\n\trequire('./localStorage'),\n\trequire('./oldFF-globalStorage'),\n\trequire('./oldIE-userDataStorage'),\n\trequire('./cookieStorage'),\n\trequire('./sessionStorage'),\n\trequire('./memoryStorage')\n]\n", "/* eslint-disable */\n\n//  json2.js\n//  2016-10-28\n//  Public Domain.\n//  NO WARRANTY EXPRESSED OR IMPLIED. USE AT YOUR OWN RISK.\n//  See http://www.JSON.org/js.html\n//  This code should be minified before deployment.\n//  See http://javascript.crockford.com/jsmin.html\n\n//  USE YOUR OWN COPY. IT IS EXTREMELY UNWISE TO LOAD CODE FROM SERVERS YOU DO\n//  NOT CONTROL.\n\n//  This file creates a global JSON object containing two methods: stringify\n//  and parse. This file provides the ES5 JSON capability to ES3 systems.\n//  If a project might run on IE8 or earlier, then this file should be included.\n//  This file does nothing on ES5 systems.\n\n//      JSON.stringify(value, replacer, space)\n//          value       any JavaScript value, usually an object or array.\n//          replacer    an optional parameter that determines how object\n//                      values are stringified for objects. It can be a\n//                      function or an array of strings.\n//          space       an optional parameter that specifies the indentation\n//                      of nested structures. If it is omitted, the text will\n//                      be packed without extra whitespace. If it is a number,\n//                      it will specify the number of spaces to indent at each\n//                      level. If it is a string (such as \"\\t\" or \"&nbsp;\"),\n//                      it contains the characters used to indent at each level.\n//          This method produces a JSON text from a JavaScript value.\n//          When an object value is found, if the object contains a toJSON\n//          method, its toJSON method will be called and the result will be\n//          stringified. A toJSON method does not serialize: it returns the\n//          value represented by the name/value pair that should be serialized,\n//          or undefined if nothing should be serialized. The toJSON method\n//          will be passed the key associated with the value, and this will be\n//          bound to the value.\n\n//          For example, this would serialize Dates as ISO strings.\n\n//              Date.prototype.toJSON = function (key) {\n//                  function f(n) {\n//                      // Format integers to have at least two digits.\n//                      return (n < 10)\n//                          ? \"0\" + n\n//                          : n;\n//                  }\n//                  return this.getUTCFullYear()   + \"-\" +\n//                       f(this.getUTCMonth() + 1) + \"-\" +\n//                       f(this.getUTCDate())      + \"T\" +\n//                       f(this.getUTCHours())     + \":\" +\n//                       f(this.getUTCMinutes())   + \":\" +\n//                       f(this.getUTCSeconds())   + \"Z\";\n//              };\n\n//          You can provide an optional replacer method. It will be passed the\n//          key and value of each member, with this bound to the containing\n//          object. The value that is returned from your method will be\n//          serialized. If your method returns undefined, then the member will\n//          be excluded from the serialization.\n\n//          If the replacer parameter is an array of strings, then it will be\n//          used to select the members to be serialized. It filters the results\n//          such that only members with keys listed in the replacer array are\n//          stringified.\n\n//          Values that do not have JSON representations, such as undefined or\n//          functions, will not be serialized. Such values in objects will be\n//          dropped; in arrays they will be replaced with null. You can use\n//          a replacer function to replace those with JSON values.\n\n//          JSON.stringify(undefined) returns undefined.\n\n//          The optional space parameter produces a stringification of the\n//          value that is filled with line breaks and indentation to make it\n//          easier to read.\n\n//          If the space parameter is a non-empty string, then that string will\n//          be used for indentation. If the space parameter is a number, then\n//          the indentation will be that many spaces.\n\n//          Example:\n\n//          text = JSON.stringify([\"e\", {pluribus: \"unum\"}]);\n//          // text is '[\"e\",{\"pluribus\":\"unum\"}]'\n\n//          text = JSON.stringify([\"e\", {pluribus: \"unum\"}], null, \"\\t\");\n//          // text is '[\\n\\t\"e\",\\n\\t{\\n\\t\\t\"pluribus\": \"unum\"\\n\\t}\\n]'\n\n//          text = JSON.stringify([new Date()], function (key, value) {\n//              return this[key] instanceof Date\n//                  ? \"Date(\" + this[key] + \")\"\n//                  : value;\n//          });\n//          // text is '[\"Date(---current time---)\"]'\n\n//      JSON.parse(text, reviver)\n//          This method parses a JSON text to produce an object or array.\n//          It can throw a SyntaxError exception.\n\n//          The optional reviver parameter is a function that can filter and\n//          transform the results. It receives each of the keys and values,\n//          and its return value is used instead of the original value.\n//          If it returns what it received, then the structure is not modified.\n//          If it returns undefined then the member is deleted.\n\n//          Example:\n\n//          // Parse the text. Values that look like ISO date strings will\n//          // be converted to Date objects.\n\n//          myData = JSON.parse(text, function (key, value) {\n//              var a;\n//              if (typeof value === \"string\") {\n//                  a =\n//   /^(\\d{4})-(\\d{2})-(\\d{2})T(\\d{2}):(\\d{2}):(\\d{2}(?:\\.\\d*)?)Z$/.exec(value);\n//                  if (a) {\n//                      return new Date(Date.UTC(+a[1], +a[2] - 1, +a[3], +a[4],\n//                          +a[5], +a[6]));\n//                  }\n//              }\n//              return value;\n//          });\n\n//          myData = JSON.parse('[\"Date(09/09/2001)\"]', function (key, value) {\n//              var d;\n//              if (typeof value === \"string\" &&\n//                      value.slice(0, 5) === \"Date(\" &&\n//                      value.slice(-1) === \")\") {\n//                  d = new Date(value.slice(5, -1));\n//                  if (d) {\n//                      return d;\n//                  }\n//              }\n//              return value;\n//          });\n\n//  This is a reference implementation. You are free to copy, modify, or\n//  redistribute.\n\n/*jslint\n    eval, for, this\n*/\n\n/*property\n    JSON, apply, call, charCodeAt, getUTCDate, getUTCFullYear, getUTCHours,\n    getUTCMinutes, getUTCMonth, getUTCSeconds, hasOwnProperty, join,\n    lastIndex, length, parse, prototype, push, replace, slice, stringify,\n    test, toJSON, toString, valueOf\n*/\n\n\n// Create a JSON object only if one does not already exist. We create the\n// methods in a closure to avoid creating global variables.\n\nif (typeof JSON !== \"object\") {\n    JSON = {};\n}\n\n(function () {\n    \"use strict\";\n\n    var rx_one = /^[\\],:{}\\s]*$/;\n    var rx_two = /\\\\(?:[\"\\\\\\/bfnrt]|u[0-9a-fA-F]{4})/g;\n    var rx_three = /\"[^\"\\\\\\n\\r]*\"|true|false|null|-?\\d+(?:\\.\\d*)?(?:[eE][+\\-]?\\d+)?/g;\n    var rx_four = /(?:^|:|,)(?:\\s*\\[)+/g;\n    var rx_escapable = /[\\\\\"\\u0000-\\u001f\\u007f-\\u009f\\u00ad\\u0600-\\u0604\\u070f\\u17b4\\u17b5\\u200c-\\u200f\\u2028-\\u202f\\u2060-\\u206f\\ufeff\\ufff0-\\uffff]/g;\n    var rx_dangerous = /[\\u0000\\u00ad\\u0600-\\u0604\\u070f\\u17b4\\u17b5\\u200c-\\u200f\\u2028-\\u202f\\u2060-\\u206f\\ufeff\\ufff0-\\uffff]/g;\n\n    function f(n) {\n        // Format integers to have at least two digits.\n        return n < 10\n            ? \"0\" + n\n            : n;\n    }\n\n    function this_value() {\n        return this.valueOf();\n    }\n\n    if (typeof Date.prototype.toJSON !== \"function\") {\n\n        Date.prototype.toJSON = function () {\n\n            return isFinite(this.valueOf())\n                ? this.getUTCFullYear() + \"-\" +\n                        f(this.getUTCMonth() + 1) + \"-\" +\n                        f(this.getUTCDate()) + \"T\" +\n                        f(this.getUTCHours()) + \":\" +\n                        f(this.getUTCMinutes()) + \":\" +\n                        f(this.getUTCSeconds()) + \"Z\"\n                : null;\n        };\n\n        Boolean.prototype.toJSON = this_value;\n        Number.prototype.toJSON = this_value;\n        String.prototype.toJSON = this_value;\n    }\n\n    var gap;\n    var indent;\n    var meta;\n    var rep;\n\n\n    function quote(string) {\n\n// If the string contains no control characters, no quote characters, and no\n// backslash characters, then we can safely slap some quotes around it.\n// Otherwise we must also replace the offending characters with safe escape\n// sequences.\n\n        rx_escapable.lastIndex = 0;\n        return rx_escapable.test(string)\n            ? \"\\\"\" + string.replace(rx_escapable, function (a) {\n                var c = meta[a];\n                return typeof c === \"string\"\n                    ? c\n                    : \"\\\\u\" + (\"0000\" + a.charCodeAt(0).toString(16)).slice(-4);\n            }) + \"\\\"\"\n            : \"\\\"\" + string + \"\\\"\";\n    }\n\n\n    function str(key, holder) {\n\n// Produce a string from holder[key].\n\n        var i;          // The loop counter.\n        var k;          // The member key.\n        var v;          // The member value.\n        var length;\n        var mind = gap;\n        var partial;\n        var value = holder[key];\n\n// If the value has a toJSON method, call it to obtain a replacement value.\n\n        if (value && typeof value === \"object\" &&\n                typeof value.toJSON === \"function\") {\n            value = value.toJSON(key);\n        }\n\n// If we were called with a replacer function, then call the replacer to\n// obtain a replacement value.\n\n        if (typeof rep === \"function\") {\n            value = rep.call(holder, key, value);\n        }\n\n// What happens next depends on the value's type.\n\n        switch (typeof value) {\n        case \"string\":\n            return quote(value);\n\n        case \"number\":\n\n// JSON numbers must be finite. Encode non-finite numbers as null.\n\n            return isFinite(value)\n                ? String(value)\n                : \"null\";\n\n        case \"boolean\":\n        case \"null\":\n\n// If the value is a boolean or null, convert it to a string. Note:\n// typeof null does not produce \"null\". The case is included here in\n// the remote chance that this gets fixed someday.\n\n            return String(value);\n\n// If the type is \"object\", we might be dealing with an object or an array or\n// null.\n\n        case \"object\":\n\n// Due to a specification blunder in ECMAScript, typeof null is \"object\",\n// so watch out for that case.\n\n            if (!value) {\n                return \"null\";\n            }\n\n// Make an array to hold the partial results of stringifying this object value.\n\n            gap += indent;\n            partial = [];\n\n// Is the value an array?\n\n            if (Object.prototype.toString.apply(value) === \"[object Array]\") {\n\n// The value is an array. Stringify every element. Use null as a placeholder\n// for non-JSON values.\n\n                length = value.length;\n                for (i = 0; i < length; i += 1) {\n                    partial[i] = str(i, value) || \"null\";\n                }\n\n// Join all of the elements together, separated with commas, and wrap them in\n// brackets.\n\n                v = partial.length === 0\n                    ? \"[]\"\n                    : gap\n                        ? \"[\\n\" + gap + partial.join(\",\\n\" + gap) + \"\\n\" + mind + \"]\"\n                        : \"[\" + partial.join(\",\") + \"]\";\n                gap = mind;\n                return v;\n            }\n\n// If the replacer is an array, use it to select the members to be stringified.\n\n            if (rep && typeof rep === \"object\") {\n                length = rep.length;\n                for (i = 0; i < length; i += 1) {\n                    if (typeof rep[i] === \"string\") {\n                        k = rep[i];\n                        v = str(k, value);\n                        if (v) {\n                            partial.push(quote(k) + (\n                                gap\n                                    ? \": \"\n                                    : \":\"\n                            ) + v);\n                        }\n                    }\n                }\n            } else {\n\n// Otherwise, iterate through all of the keys in the object.\n\n                for (k in value) {\n                    if (Object.prototype.hasOwnProperty.call(value, k)) {\n                        v = str(k, value);\n                        if (v) {\n                            partial.push(quote(k) + (\n                                gap\n                                    ? \": \"\n                                    : \":\"\n                            ) + v);\n                        }\n                    }\n                }\n            }\n\n// Join all of the member texts together, separated with commas,\n// and wrap them in braces.\n\n            v = partial.length === 0\n                ? \"{}\"\n                : gap\n                    ? \"{\\n\" + gap + partial.join(\",\\n\" + gap) + \"\\n\" + mind + \"}\"\n                    : \"{\" + partial.join(\",\") + \"}\";\n            gap = mind;\n            return v;\n        }\n    }\n\n// If the JSON object does not yet have a stringify method, give it one.\n\n    if (typeof JSON.stringify !== \"function\") {\n        meta = {    // table of character substitutions\n            \"\\b\": \"\\\\b\",\n            \"\\t\": \"\\\\t\",\n            \"\\n\": \"\\\\n\",\n            \"\\f\": \"\\\\f\",\n            \"\\r\": \"\\\\r\",\n            \"\\\"\": \"\\\\\\\"\",\n            \"\\\\\": \"\\\\\\\\\"\n        };\n        JSON.stringify = function (value, replacer, space) {\n\n// The stringify method takes a value and an optional replacer, and an optional\n// space parameter, and returns a JSON text. The replacer can be a function\n// that can replace values, or an array of strings that will select the keys.\n// A default replacer method can be provided. Use of the space parameter can\n// produce text that is more easily readable.\n\n            var i;\n            gap = \"\";\n            indent = \"\";\n\n// If the space parameter is a number, make an indent string containing that\n// many spaces.\n\n            if (typeof space === \"number\") {\n                for (i = 0; i < space; i += 1) {\n                    indent += \" \";\n                }\n\n// If the space parameter is a string, it will be used as the indent string.\n\n            } else if (typeof space === \"string\") {\n                indent = space;\n            }\n\n// If there is a replacer, it must be a function or an array.\n// Otherwise, throw an error.\n\n            rep = replacer;\n            if (replacer && typeof replacer !== \"function\" &&\n                    (typeof replacer !== \"object\" ||\n                    typeof replacer.length !== \"number\")) {\n                throw new Error(\"JSON.stringify\");\n            }\n\n// Make a fake root object containing our value under the key of \"\".\n// Return the result of stringifying the value.\n\n            return str(\"\", {\"\": value});\n        };\n    }\n\n\n// If the JSON object does not yet have a parse method, give it one.\n\n    if (typeof JSON.parse !== \"function\") {\n        JSON.parse = function (text, reviver) {\n\n// The parse method takes a text and an optional reviver function, and returns\n// a JavaScript value if the text is a valid JSON text.\n\n            var j;\n\n            function walk(holder, key) {\n\n// The walk method is used to recursively walk the resulting structure so\n// that modifications can be made.\n\n                var k;\n                var v;\n                var value = holder[key];\n                if (value && typeof value === \"object\") {\n                    for (k in value) {\n                        if (Object.prototype.hasOwnProperty.call(value, k)) {\n                            v = walk(value, k);\n                            if (v !== undefined) {\n                                value[k] = v;\n                            } else {\n                                delete value[k];\n                            }\n                        }\n                    }\n                }\n                return reviver.call(holder, key, value);\n            }\n\n\n// Parsing happens in four stages. In the first stage, we replace certain\n// Unicode characters with escape sequences. JavaScript handles many characters\n// incorrectly, either silently deleting them, or treating them as line endings.\n\n            text = String(text);\n            rx_dangerous.lastIndex = 0;\n            if (rx_dangerous.test(text)) {\n                text = text.replace(rx_dangerous, function (a) {\n                    return \"\\\\u\" +\n                            (\"0000\" + a.charCodeAt(0).toString(16)).slice(-4);\n                });\n            }\n\n// In the second stage, we run the text against regular expressions that look\n// for non-JSON patterns. We are especially concerned with \"()\" and \"new\"\n// because they can cause invocation, and \"=\" because it can cause mutation.\n// But just to be safe, we want to reject all unexpected forms.\n\n// We split the second stage into 4 regexp operations in order to work around\n// crippling inefficiencies in IE's and Safari's regexp engines. First we\n// replace the JSON backslash pairs with \"@\" (a non-JSON character). Second, we\n// replace all simple value tokens with \"]\" characters. Third, we delete all\n// open brackets that follow a colon or comma or that begin the text. Finally,\n// we look to see that the remaining characters are only whitespace or \"]\" or\n// \",\" or \":\" or \"{\" or \"}\". If that is so, then the text is safe for eval.\n\n            if (\n                rx_one.test(\n                    text\n                        .replace(rx_two, \"@\")\n                        .replace(rx_three, \"]\")\n                        .replace(rx_four, \"\")\n                )\n            ) {\n\n// In the third stage we use the eval function to compile the text into a\n// JavaScript structure. The \"{\" operator is subject to a syntactic ambiguity\n// in JavaScript: it can begin a block or an object literal. We wrap the text\n// in parens to eliminate the ambiguity.\n\n                j = eval(\"(\" + text + \")\");\n\n// In the optional fourth stage, we recursively walk the new structure, passing\n// each name/value pair to a reviver function for possible transformation.\n\n                return (typeof reviver === \"function\")\n                    ? walk({\"\": j}, \"\")\n                    : j;\n            }\n\n// If the text is not JSON parseable, then a SyntaxError is thrown.\n\n            throw new SyntaxError(\"JSON.parse\");\n        };\n    }\n}());", "module.exports = json2Plugin\n\nfunction json2Plugin() {\n\trequire('./lib/json2')\n\treturn {}\n}\n", "var engine = require('../src/store-engine')\n\nvar storages = require('../storages/all')\nvar plugins = [require('../plugins/json2')]\n\nmodule.exports = engine.createStore(storages, plugins)\n"], "mappings": ";;;;;AAAA;AAAA,mCAAAA,UAAAC,SAAA;AAAA,QAAI,SAAS,YAAY;AACzB,QAAI,SAAS,YAAY;AACzB,QAAI,OAAO,UAAU;AACrB,QAAI,SAAU,OAAO,WAAW,cAAc,SAAS;AAEvD,IAAAA,QAAO,UAAU;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAEA,aAAS,cAAc;AACtB,UAAI,OAAO,QAAQ;AAClB,eAAO,OAAO;AAAA,MACf,OAAO;AACN,eAAO,SAAS,WAAW,KAAK,QAAQ,QAAQ,KAAK;AACpD,mBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,iBAAK,OAAO,UAAU,CAAC,CAAC,GAAG,SAAS,KAAK,KAAK;AAC7C,kBAAI,GAAG,IAAI;AAAA,YACZ,CAAC;AAAA,UACF;AACA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAEA,aAAS,cAAc;AACtB,UAAI,OAAO,QAAQ;AAClB,eAAO,SAASC,QAAO,KAAK,cAAc,cAAc,KAAK;AAC5D,cAAI,iBAAiB,MAAM,WAAW,CAAC;AACvC,iBAAO,OAAO,MAAM,MAAM,CAAC,OAAO,OAAO,GAAG,CAAC,EAAE,OAAO,cAAc,CAAC;AAAA,QACtE;AAAA,MACD,OAAO;AACN,YAASC,KAAT,WAAa;AAAA,QAAC;AAAL,gBAAAA;AACT,eAAO,SAASD,QAAO,KAAK,cAAc,cAAc,KAAK;AAC5D,cAAI,iBAAiB,MAAM,WAAW,CAAC;AACvC,UAAAC,GAAE,YAAY;AACd,iBAAO,OAAO,MAAM,MAAM,CAAC,IAAIA,GAAE,CAAC,EAAE,OAAO,cAAc,CAAC;AAAA,QAC3D;AAAA,MACD;AAAA,IACD;AAEA,aAAS,YAAY;AACpB,UAAI,OAAO,UAAU,MAAM;AAC1B,eAAO,SAASC,MAAKC,MAAK;AACzB,iBAAO,OAAO,UAAU,KAAK,KAAKA,IAAG;AAAA,QACtC;AAAA,MACD,OAAO;AACN,eAAO,SAASD,MAAKC,MAAK;AACzB,iBAAOA,KAAI,QAAQ,sCAAsC,EAAE;AAAA,QAC5D;AAAA,MACD;AAAA,IACD;AAEA,aAAS,KAAK,KAAK,IAAI;AACtB,aAAO,WAAW;AACjB,eAAO,GAAG,MAAM,KAAK,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC,CAAC;AAAA,MAC9D;AAAA,IACD;AAEA,aAAS,MAAM,KAAK,OAAO;AAC1B,aAAO,MAAM,UAAU,MAAM,KAAK,KAAK,SAAS,CAAC;AAAA,IAClD;AAEA,aAAS,KAAK,KAAK,IAAI;AACtB,YAAM,KAAK,SAAS,KAAK,KAAK;AAC7B,WAAG,KAAK,GAAG;AACX,eAAO;AAAA,MACR,CAAC;AAAA,IACF;AAEA,aAAS,IAAI,KAAK,IAAI;AACrB,UAAI,MAAO,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;AAC/B,YAAM,KAAK,SAAS,GAAG,GAAG;AACzB,YAAI,CAAC,IAAI,GAAG,GAAG,CAAC;AAChB,eAAO;AAAA,MACR,CAAC;AACD,aAAO;AAAA,IACR;AAEA,aAAS,MAAM,KAAK,IAAI;AACvB,UAAI,OAAO,GAAG,GAAG;AAChB,iBAAS,IAAE,GAAG,IAAE,IAAI,QAAQ,KAAK;AAChC,cAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG;AAClB,mBAAO,IAAI,CAAC;AAAA,UACb;AAAA,QACD;AAAA,MACD,OAAO;AACN,iBAAS,OAAO,KAAK;AACpB,cAAI,IAAI,eAAe,GAAG,GAAG;AAC5B,gBAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG;AACtB,qBAAO,IAAI,GAAG;AAAA,YACf;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,aAAS,OAAO,KAAK;AACpB,aAAQ,OAAO,QAAQ,OAAO,OAAO,cAAc,OAAO,IAAI,UAAU;AAAA,IACzE;AAEA,aAAS,WAAW,KAAK;AACxB,aAAO,OAAO,CAAC,EAAE,SAAS,KAAK,GAAG,MAAM;AAAA,IACzC;AAEA,aAAS,SAAS,KAAK;AACtB,aAAO,OAAO,CAAC,EAAE,SAAS,KAAK,GAAG,MAAM;AAAA,IACzC;AAAA;AAAA;;;ACrHA;AAAA,2CAAAC,UAAAC,SAAA;AAAA,QAAI,OAAO;AACX,QAAI,QAAQ,KAAK;AACjB,QAAI,QAAQ,KAAK;AACjB,QAAI,OAAO,KAAK;AAChB,QAAI,OAAO,KAAK;AAChB,QAAI,SAAS,KAAK;AAClB,QAAI,SAAS,KAAK;AAClB,QAAI,aAAa,KAAK;AACtB,QAAI,WAAW,KAAK;AAEpB,IAAAA,QAAO,UAAU;AAAA,MAChB;AAAA,IACD;AAEA,QAAI,WAAW;AAAA,MACd,SAAS;AAAA,MACT,SAAS;AAAA;AAAA;AAAA,MAIT,KAAK,SAAS,KAAK,sBAAsB;AACxC,YAAI,OAAO,KAAK,QAAQ,KAAK,KAAK,mBAAmB,GAAG;AACxD,eAAO,KAAK,aAAa,MAAM,oBAAoB;AAAA,MACpD;AAAA;AAAA;AAAA,MAIA,KAAK,SAAS,KAAK,OAAO;AACzB,YAAI,UAAU,QAAW;AACxB,iBAAO,KAAK,OAAO,GAAG;AAAA,QACvB;AACA,aAAK,QAAQ,MAAM,KAAK,mBAAmB,KAAK,KAAK,WAAW,KAAK,CAAC;AACtE,eAAO;AAAA,MACR;AAAA;AAAA,MAGA,QAAQ,SAAS,KAAK;AACrB,aAAK,QAAQ,OAAO,KAAK,mBAAmB,GAAG;AAAA,MAChD;AAAA;AAAA;AAAA,MAIA,MAAM,SAAS,UAAU;AACxB,YAAI,OAAO;AACX,aAAK,QAAQ,KAAK,SAAS,KAAK,eAAe;AAC9C,mBAAS,KAAK,MAAM,KAAK,aAAa,GAAG,IAAI,iBAAiB,IAAI,QAAQ,KAAK,kBAAkB,EAAE,CAAC;AAAA,QACrG,CAAC;AAAA,MACF;AAAA;AAAA,MAGA,UAAU,WAAW;AACpB,aAAK,QAAQ,SAAS;AAAA,MACvB;AAAA;AAAA;AAAA;AAAA,MAMA,cAAc,SAAS,WAAW;AACjC,eAAQ,KAAK,oBAAoB,eAAa,YAAU;AAAA,MACzD;AAAA;AAAA;AAAA;AAAA,MAKA,aAAa,WAAW;AACvB,eAAO,YAAY,MAAM,MAAM,SAAS;AAAA,MACzC;AAAA,MAEA,WAAW,SAAS,QAAQ;AAC3B,aAAK,WAAW,MAAM;AAAA,MACvB;AAAA,MAEA,WAAW,SAAS,WAAW;AAC9B,eAAO,YAAY,KAAK,SAAS,KAAK,SAAS,SAAS;AAAA,MACzD;AAAA,IACD;AAEA,aAAS,QAAQ;AAChB,UAAI,WAAY,OAAO,WAAW,cAAc,OAAO;AACvD,UAAI,CAAC,UAAU;AAAE;AAAA,MAAO;AACxB,UAAI,KAAM,SAAS,OAAO,SAAS,OAAO,SAAS;AACnD,SAAG,MAAM,UAAU,SAAS;AAAA,IAC7B;AAEA,aAAS,YAAY,UAAU,SAAS,WAAW;AAClD,UAAI,CAAC,WAAW;AACf,oBAAY;AAAA,MACb;AACA,UAAI,YAAY,CAAC,OAAO,QAAQ,GAAG;AAClC,mBAAW,CAAC,QAAQ;AAAA,MACrB;AACA,UAAI,WAAW,CAAC,OAAO,OAAO,GAAG;AAChC,kBAAU,CAAC,OAAO;AAAA,MACnB;AAEA,UAAI,kBAAmB,YAAY,eAAa,YAAU,MAAM;AAChE,UAAI,kBAAmB,YAAY,IAAI,OAAO,MAAI,eAAe,IAAI;AACrE,UAAI,kBAAkB;AACtB,UAAI,CAAC,gBAAgB,KAAK,SAAS,GAAG;AACrC,cAAM,IAAI,MAAM,0EAA0E;AAAA,MAC3F;AAEA,UAAI,qBAAqB;AAAA,QACxB,kBAAkB;AAAA,QAClB,kBAAkB;AAAA,QAElB,cAAc,SAAS,SAAS;AAC/B,cAAI;AACH,gBAAI,UAAU;AACd,oBAAQ,MAAM,SAAS,OAAO;AAC9B,gBAAI,KAAM,QAAQ,KAAK,OAAO,MAAM;AACpC,oBAAQ,OAAO,OAAO;AACtB,mBAAO;AAAA,UACR,SAAQ,GAAG;AACV,mBAAO;AAAA,UACR;AAAA,QACD;AAAA,QAEA,qBAAqB,SAAS,cAAc,UAAU;AACrD,cAAI,QAAQ,KAAK,QAAQ;AACzB,eAAK,QAAQ,IAAI,SAAS,WAAW;AACpC,gBAAI,OAAO,MAAM,WAAW,CAAC;AAC7B,gBAAI,OAAO;AAIX,qBAAS,WAAW;AACnB,kBAAI,CAAC,OAAO;AAAE;AAAA,cAAO;AACrB,mBAAK,WAAW,SAAS,KAAK,GAAG;AAChC,qBAAK,CAAC,IAAI;AAAA,cACX,CAAC;AACD,qBAAO,MAAM,MAAM,MAAM,IAAI;AAAA,YAC9B;AAIA,gBAAI,YAAY,CAAC,QAAQ,EAAE,OAAO,IAAI;AAEtC,mBAAO,aAAa,MAAM,MAAM,SAAS;AAAA,UAC1C;AAAA,QACD;AAAA,QAEA,YAAY,SAAS,KAAK;AACzB,iBAAO,KAAK,UAAU,GAAG;AAAA,QAC1B;AAAA,QAEA,cAAc,SAAS,QAAQ,YAAY;AAC1C,cAAI,CAAC,QAAQ;AAAE,mBAAO;AAAA,UAAW;AAMjC,cAAI,MAAM;AACV,cAAI;AAAE,kBAAM,KAAK,MAAM,MAAM;AAAA,UAAE,SACzB,GAAG;AAAE,kBAAM;AAAA,UAAO;AAExB,iBAAQ,QAAQ,SAAY,MAAM;AAAA,QACnC;AAAA,QAEA,aAAa,SAAS,SAAS;AAC9B,cAAI,KAAK,SAAS;AAAE;AAAA,UAAO;AAC3B,cAAI,KAAK,aAAa,OAAO,GAAG;AAC/B,iBAAK,UAAU;AACf,iBAAK,UAAU;AAAA,UAChB;AAAA,QACD;AAAA,QAEA,YAAY,SAAS,QAAQ;AAC5B,cAAI,OAAO;AAIX,cAAI,OAAO,MAAM,GAAG;AACnB,iBAAK,QAAQ,SAASC,SAAQ;AAC7B,mBAAK,WAAWA,OAAM;AAAA,YACvB,CAAC;AACD;AAAA,UACD;AAIA,cAAI,aAAa,MAAM,KAAK,SAAS,SAASC,aAAY;AACzD,mBAAQ,WAAWA;AAAA,UACpB,CAAC;AACD,cAAI,YAAY;AACf;AAAA,UACD;AACA,eAAK,QAAQ,KAAK,MAAM;AAGxB,cAAI,CAAC,WAAW,MAAM,GAAG;AACxB,kBAAM,IAAI,MAAM,qDAAqD;AAAA,UACtE;AAEA,cAAI,mBAAmB,OAAO,KAAK,IAAI;AACvC,cAAI,CAAC,SAAS,gBAAgB,GAAG;AAChC,kBAAM,IAAI,MAAM,sDAAsD;AAAA,UACvE;AAGA,eAAK,kBAAkB,SAAS,cAAc,UAAU;AACvD,gBAAI,CAAC,WAAW,YAAY,GAAG;AAC9B,oBAAM,IAAI,MAAM,0BAAwB,WAAS,kBAAgB,OAAO,OAAK,yCAAyC;AAAA,YACvH;AACA,iBAAK,oBAAoB,cAAc,QAAQ;AAAA,UAChD,CAAC;AAAA,QACF;AAAA;AAAA;AAAA;AAAA,QAMA,YAAY,SAAS,SAAS;AAC7B,gBAAM,sEAAsE;AAC5E,eAAK,YAAY,OAAO;AAAA,QACzB;AAAA,MACD;AAEA,UAAI,QAAQ,OAAO,oBAAoB,UAAU;AAAA,QAChD,SAAS,CAAC;AAAA,MACX,CAAC;AACD,YAAM,MAAM,CAAC;AACb,WAAK,OAAO,SAAS,MAAM,UAAU;AACpC,YAAI,WAAW,IAAI,GAAG;AACrB,gBAAM,IAAI,QAAQ,IAAI,KAAK,OAAO,IAAI;AAAA,QACvC;AAAA,MACD,CAAC;AACD,WAAK,UAAU,SAAS,SAAS;AAChC,cAAM,YAAY,OAAO;AAAA,MAC1B,CAAC;AACD,WAAK,SAAS,SAAS,QAAQ;AAC9B,cAAM,WAAW,MAAM;AAAA,MACxB,CAAC;AACD,aAAO;AAAA,IACR;AAAA;AAAA;;;AC5OA;AAAA,gDAAAC,UAAAC,SAAA;AAAA,QAAI,OAAO;AACX,QAAI,SAAS,KAAK;AAElB,IAAAA,QAAO,UAAU;AAAA,MAChB,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAEA,aAAS,eAAe;AACvB,aAAO,OAAO;AAAA,IACf;AAEA,aAAS,KAAK,KAAK;AAClB,aAAO,aAAa,EAAE,QAAQ,GAAG;AAAA,IAClC;AAEA,aAAS,MAAM,KAAK,MAAM;AACzB,aAAO,aAAa,EAAE,QAAQ,KAAK,IAAI;AAAA,IACxC;AAEA,aAAS,KAAK,IAAI;AACjB,eAAS,IAAI,aAAa,EAAE,SAAS,GAAG,KAAK,GAAG,KAAK;AACpD,YAAI,MAAM,aAAa,EAAE,IAAI,CAAC;AAC9B,WAAG,KAAK,GAAG,GAAG,GAAG;AAAA,MAClB;AAAA,IACD;AAEA,aAAS,OAAO,KAAK;AACpB,aAAO,aAAa,EAAE,WAAW,GAAG;AAAA,IACrC;AAEA,aAAS,WAAW;AACnB,aAAO,aAAa,EAAE,MAAM;AAAA,IAC7B;AAAA;AAAA;;;ACrCA;AAAA,uDAAAC,UAAAC,SAAA;AAIA,QAAI,OAAO;AACX,QAAI,SAAS,KAAK;AAElB,IAAAA,QAAO,UAAU;AAAA,MAChB,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAEA,QAAI,gBAAgB,OAAO;AAE3B,aAAS,KAAK,KAAK;AAClB,aAAO,cAAc,GAAG;AAAA,IACzB;AAEA,aAAS,MAAM,KAAK,MAAM;AACzB,oBAAc,GAAG,IAAI;AAAA,IACtB;AAEA,aAAS,KAAK,IAAI;AACjB,eAAS,IAAI,cAAc,SAAS,GAAG,KAAK,GAAG,KAAK;AACnD,YAAI,MAAM,cAAc,IAAI,CAAC;AAC7B,WAAG,cAAc,GAAG,GAAG,GAAG;AAAA,MAC3B;AAAA,IACD;AAEA,aAAS,OAAO,KAAK;AACpB,aAAO,cAAc,WAAW,GAAG;AAAA,IACpC;AAEA,aAAS,WAAW;AACnB,WAAK,SAAS,KAAK,GAAG;AACrB,eAAO,cAAc,GAAG;AAAA,MACzB,CAAC;AAAA,IACF;AAAA;AAAA;;;ACzCA;AAAA,yDAAAC,UAAAC,SAAA;AAIA,QAAI,OAAO;AACX,QAAI,SAAS,KAAK;AAElB,IAAAA,QAAO,UAAU;AAAA,MAChB,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAEA,QAAI,cAAc;AAClB,QAAI,MAAM,OAAO;AACjB,QAAI,iBAAiB,yBAAyB;AAC9C,QAAI,WAAW,OAAO,YAAY,OAAO,UAAU,YAAY,IAAI,MAAM,4BAA4B;AAErG,aAAS,MAAM,YAAY,MAAM;AAChC,UAAI,SAAS;AAAE;AAAA,MAAO;AACtB,UAAI,WAAW,OAAO,UAAU;AAChC,qBAAe,SAAS,WAAW;AAClC,kBAAU,aAAa,UAAU,IAAI;AACrC,kBAAU,KAAK,WAAW;AAAA,MAC3B,CAAC;AAAA,IACF;AAEA,aAAS,KAAK,YAAY;AACzB,UAAI,SAAS;AAAE;AAAA,MAAO;AACtB,UAAI,WAAW,OAAO,UAAU;AAChC,UAAI,MAAM;AACV,qBAAe,SAAS,WAAW;AAClC,cAAM,UAAU,aAAa,QAAQ;AAAA,MACtC,CAAC;AACD,aAAO;AAAA,IACR;AAEA,aAAS,KAAK,UAAU;AACvB,qBAAe,SAAS,WAAW;AAClC,YAAI,aAAa,UAAU,YAAY,gBAAgB;AACvD,iBAAS,IAAE,WAAW,SAAO,GAAG,KAAG,GAAG,KAAK;AAC1C,cAAI,OAAO,WAAW,CAAC;AACvB,mBAAS,UAAU,aAAa,KAAK,IAAI,GAAG,KAAK,IAAI;AAAA,QACtD;AAAA,MACD,CAAC;AAAA,IACF;AAEA,aAAS,OAAO,YAAY;AAC3B,UAAI,WAAW,OAAO,UAAU;AAChC,qBAAe,SAAS,WAAW;AAClC,kBAAU,gBAAgB,QAAQ;AAClC,kBAAU,KAAK,WAAW;AAAA,MAC3B,CAAC;AAAA,IACF;AAEA,aAAS,WAAW;AACnB,qBAAe,SAAS,WAAW;AAClC,YAAI,aAAa,UAAU,YAAY,gBAAgB;AACvD,kBAAU,KAAK,WAAW;AAC1B,iBAAS,IAAE,WAAW,SAAO,GAAG,KAAG,GAAG,KAAK;AAC1C,oBAAU,gBAAgB,WAAW,CAAC,EAAE,IAAI;AAAA,QAC7C;AACA,kBAAU,KAAK,WAAW;AAAA,MAC3B,CAAC;AAAA,IACF;AAQA,QAAI,sBAAsB,IAAI,OAAO,yCAAyC,GAAG;AACjF,aAAS,OAAO,KAAK;AACpB,aAAO,IAAI,QAAQ,OAAO,OAAO,EAAE,QAAQ,qBAAqB,KAAK;AAAA,IACtE;AAEA,aAAS,2BAA2B;AACnC,UAAI,CAAC,OAAO,CAAC,IAAI,mBAAmB,CAAC,IAAI,gBAAgB,aAAa;AACrE,eAAO;AAAA,MACR;AACA,UAAI,YAAY,UACf,cACA,kBACA;AAYD,UAAI;AAEH,2BAAmB,IAAI,cAAc,UAAU;AAC/C,yBAAiB,KAAK;AACtB,yBAAiB,MAAM,MAAI,YAAU,yBAAuB,YAAU,uCAAuC;AAC7G,yBAAiB,MAAM;AACvB,uBAAe,iBAAiB,EAAE,OAAO,CAAC,EAAE;AAC5C,oBAAY,aAAa,cAAc,KAAK;AAAA,MAC7C,SAAQ,GAAG;AAGV,oBAAY,IAAI,cAAc,KAAK;AACnC,uBAAe,IAAI;AAAA,MACpB;AAEA,aAAO,SAAS,eAAe;AAC9B,YAAI,OAAO,CAAC,EAAE,MAAM,KAAK,WAAW,CAAC;AACrC,aAAK,QAAQ,SAAS;AAGtB,qBAAa,YAAY,SAAS;AAClC,kBAAU,YAAY,mBAAmB;AACzC,kBAAU,KAAK,WAAW;AAC1B,sBAAc,MAAM,MAAM,IAAI;AAC9B,qBAAa,YAAY,SAAS;AAClC;AAAA,MACD;AAAA,IACD;AAAA;AAAA;;;AC9HA;AAAA,iDAAAC,UAAAC,SAAA;AAIA,QAAI,OAAO;AACX,QAAI,SAAS,KAAK;AAClB,QAAI,OAAO,KAAK;AAEhB,IAAAA,QAAO,UAAU;AAAA,MAChB,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAEA,QAAI,MAAM,OAAO;AAEjB,aAAS,KAAK,KAAK;AAClB,UAAI,CAAC,OAAO,CAAC,KAAK,GAAG,GAAG;AAAE,eAAO;AAAA,MAAK;AACtC,UAAI,YAAY,kBACf,OAAO,GAAG,EAAE,QAAQ,eAAe,MAAM,IACzC;AACD,aAAO,SAAS,IAAI,OAAO,QAAQ,IAAI,OAAO,SAAS,GAAG,IAAI,CAAC;AAAA,IAChE;AAEA,aAAS,KAAK,UAAU;AACvB,UAAI,UAAU,IAAI,OAAO,MAAM,MAAM;AACrC,eAAS,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,YAAI,CAAC,KAAK,QAAQ,CAAC,CAAC,GAAG;AACtB;AAAA,QACD;AACA,YAAI,MAAM,QAAQ,CAAC,EAAE,MAAM,GAAG;AAC9B,YAAI,MAAM,SAAS,IAAI,CAAC,CAAC;AACzB,YAAI,MAAM,SAAS,IAAI,CAAC,CAAC;AACzB,iBAAS,KAAK,GAAG;AAAA,MAClB;AAAA,IACD;AAEA,aAAS,MAAM,KAAK,MAAM;AACzB,UAAG,CAAC,KAAK;AAAE;AAAA,MAAO;AAClB,UAAI,SAAS,OAAO,GAAG,IAAI,MAAM,OAAO,IAAI,IAAI;AAAA,IACjD;AAEA,aAAS,OAAO,KAAK;AACpB,UAAI,CAAC,OAAO,CAAC,KAAK,GAAG,GAAG;AACvB;AAAA,MACD;AACA,UAAI,SAAS,OAAO,GAAG,IAAI;AAAA,IAC5B;AAEA,aAAS,WAAW;AACnB,WAAK,SAAS,GAAG,KAAK;AACrB,eAAO,GAAG;AAAA,MACX,CAAC;AAAA,IACF;AAEA,aAAS,KAAK,KAAK;AAClB,aAAQ,IAAI,OAAO,gBAAgB,OAAO,GAAG,EAAE,QAAQ,eAAe,MAAM,IAAI,SAAS,EAAG,KAAK,IAAI,MAAM;AAAA,IAC5G;AAAA;AAAA;;;AC5DA;AAAA,kDAAAC,UAAAC,SAAA;AAAA,QAAI,OAAO;AACX,QAAI,SAAS,KAAK;AAElB,IAAAA,QAAO,UAAU;AAAA,MAChB,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAEA,aAAS,iBAAiB;AACzB,aAAO,OAAO;AAAA,IACf;AAEA,aAAS,KAAK,KAAK;AAClB,aAAO,eAAe,EAAE,QAAQ,GAAG;AAAA,IACpC;AAEA,aAAS,MAAM,KAAK,MAAM;AACzB,aAAO,eAAe,EAAE,QAAQ,KAAK,IAAI;AAAA,IAC1C;AAEA,aAAS,KAAK,IAAI;AACjB,eAAS,IAAI,eAAe,EAAE,SAAS,GAAG,KAAK,GAAG,KAAK;AACtD,YAAI,MAAM,eAAe,EAAE,IAAI,CAAC;AAChC,WAAG,KAAK,GAAG,GAAG,GAAG;AAAA,MAClB;AAAA,IACD;AAEA,aAAS,OAAO,KAAK;AACpB,aAAO,eAAe,EAAE,WAAW,GAAG;AAAA,IACvC;AAEA,aAAS,WAAW;AACnB,aAAO,eAAe,EAAE,MAAM;AAAA,IAC/B;AAAA;AAAA;;;ACrCA;AAAA,iDAAAC,UAAAC,SAAA;AAKA,IAAAA,QAAO,UAAU;AAAA,MAChB,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAEA,QAAI,gBAAgB,CAAC;AAErB,aAAS,KAAK,KAAK;AAClB,aAAO,cAAc,GAAG;AAAA,IACzB;AAEA,aAAS,MAAM,KAAK,MAAM;AACzB,oBAAc,GAAG,IAAI;AAAA,IACtB;AAEA,aAAS,KAAK,UAAU;AACvB,eAAS,OAAO,eAAe;AAC9B,YAAI,cAAc,eAAe,GAAG,GAAG;AACtC,mBAAS,cAAc,GAAG,GAAG,GAAG;AAAA,QACjC;AAAA,MACD;AAAA,IACD;AAEA,aAAS,OAAO,KAAK;AACpB,aAAO,cAAc,GAAG;AAAA,IACzB;AAEA,aAAS,SAAS,KAAK;AACtB,sBAAgB,CAAC;AAAA,IAClB;AAAA;AAAA;;;ACtCA;AAAA,uCAAAC,UAAAC,SAAA;AAAA,IAAAA,QAAO,UAAU;AAAA;AAAA,MAEhB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA;AAAA;;;ACRA;AAAA;AA2JA,QAAI,OAAO,SAAS,UAAU;AAC1B,aAAO,CAAC;AAAA,IACZ;AAEA,KAAC,WAAY;AACT;AAEA,UAAI,SAAS;AACb,UAAI,SAAS;AACb,UAAI,WAAW;AACf,UAAI,UAAU;AACd,UAAI,eAAe;AACnB,UAAI,eAAe;AAEnB,eAAS,EAAE,GAAG;AAEV,eAAO,IAAI,KACL,MAAM,IACN;AAAA,MACV;AAEA,eAAS,aAAa;AAClB,eAAO,KAAK,QAAQ;AAAA,MACxB;AAEA,UAAI,OAAO,KAAK,UAAU,WAAW,YAAY;AAE7C,aAAK,UAAU,SAAS,WAAY;AAEhC,iBAAO,SAAS,KAAK,QAAQ,CAAC,IACxB,KAAK,eAAe,IAAI,MAClB,EAAE,KAAK,YAAY,IAAI,CAAC,IAAI,MAC5B,EAAE,KAAK,WAAW,CAAC,IAAI,MACvB,EAAE,KAAK,YAAY,CAAC,IAAI,MACxB,EAAE,KAAK,cAAc,CAAC,IAAI,MAC1B,EAAE,KAAK,cAAc,CAAC,IAAI,MAChC;AAAA,QACV;AAEA,gBAAQ,UAAU,SAAS;AAC3B,eAAO,UAAU,SAAS;AAC1B,eAAO,UAAU,SAAS;AAAA,MAC9B;AAEA,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AAGJ,eAAS,MAAM,QAAQ;AAOnB,qBAAa,YAAY;AACzB,eAAO,aAAa,KAAK,MAAM,IACzB,MAAO,OAAO,QAAQ,cAAc,SAAU,GAAG;AAC/C,cAAI,IAAI,KAAK,CAAC;AACd,iBAAO,OAAO,MAAM,WACd,IACA,SAAS,SAAS,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,MAAM,EAAE;AAAA,QAClE,CAAC,IAAI,MACH,MAAO,SAAS;AAAA,MAC1B;AAGA,eAAS,IAAI,KAAK,QAAQ;AAItB,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI,OAAO;AACX,YAAI;AACJ,YAAI,QAAQ,OAAO,GAAG;AAItB,YAAI,SAAS,OAAO,UAAU,YACtB,OAAO,MAAM,WAAW,YAAY;AACxC,kBAAQ,MAAM,OAAO,GAAG;AAAA,QAC5B;AAKA,YAAI,OAAO,QAAQ,YAAY;AAC3B,kBAAQ,IAAI,KAAK,QAAQ,KAAK,KAAK;AAAA,QACvC;AAIA,gBAAQ,OAAO,OAAO;AAAA,UACtB,KAAK;AACD,mBAAO,MAAM,KAAK;AAAA,UAEtB,KAAK;AAID,mBAAO,SAAS,KAAK,IACf,OAAO,KAAK,IACZ;AAAA,UAEV,KAAK;AAAA,UACL,KAAK;AAMD,mBAAO,OAAO,KAAK;AAAA,UAKvB,KAAK;AAKD,gBAAI,CAAC,OAAO;AACR,qBAAO;AAAA,YACX;AAIA,mBAAO;AACP,sBAAU,CAAC;AAIX,gBAAI,OAAO,UAAU,SAAS,MAAM,KAAK,MAAM,kBAAkB;AAK7D,uBAAS,MAAM;AACf,mBAAK,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAC5B,wBAAQ,CAAC,IAAI,IAAI,GAAG,KAAK,KAAK;AAAA,cAClC;AAKA,kBAAI,QAAQ,WAAW,IACjB,OACA,MACI,QAAQ,MAAM,QAAQ,KAAK,QAAQ,GAAG,IAAI,OAAO,OAAO,MACxD,MAAM,QAAQ,KAAK,GAAG,IAAI;AACpC,oBAAM;AACN,qBAAO;AAAA,YACX;AAIA,gBAAI,OAAO,OAAO,QAAQ,UAAU;AAChC,uBAAS,IAAI;AACb,mBAAK,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAC5B,oBAAI,OAAO,IAAI,CAAC,MAAM,UAAU;AAC5B,sBAAI,IAAI,CAAC;AACT,sBAAI,IAAI,GAAG,KAAK;AAChB,sBAAI,GAAG;AACH,4BAAQ,KAAK,MAAM,CAAC,KAChB,MACM,OACA,OACN,CAAC;AAAA,kBACT;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ,OAAO;AAIH,mBAAK,KAAK,OAAO;AACb,oBAAI,OAAO,UAAU,eAAe,KAAK,OAAO,CAAC,GAAG;AAChD,sBAAI,IAAI,GAAG,KAAK;AAChB,sBAAI,GAAG;AACH,4BAAQ,KAAK,MAAM,CAAC,KAChB,MACM,OACA,OACN,CAAC;AAAA,kBACT;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ;AAKA,gBAAI,QAAQ,WAAW,IACjB,OACA,MACI,QAAQ,MAAM,QAAQ,KAAK,QAAQ,GAAG,IAAI,OAAO,OAAO,MACxD,MAAM,QAAQ,KAAK,GAAG,IAAI;AACpC,kBAAM;AACN,mBAAO;AAAA,QACX;AAAA,MACJ;AAIA,UAAI,OAAO,KAAK,cAAc,YAAY;AACtC,eAAO;AAAA;AAAA,UACH,MAAM;AAAA,UACN,KAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAM;AAAA,UACN,MAAM;AAAA,QACV;AACA,aAAK,YAAY,SAAU,OAAO,UAAU,OAAO;AAQ/C,cAAI;AACJ,gBAAM;AACN,mBAAS;AAKT,cAAI,OAAO,UAAU,UAAU;AAC3B,iBAAK,IAAI,GAAG,IAAI,OAAO,KAAK,GAAG;AAC3B,wBAAU;AAAA,YACd;AAAA,UAIJ,WAAW,OAAO,UAAU,UAAU;AAClC,qBAAS;AAAA,UACb;AAKA,gBAAM;AACN,cAAI,YAAY,OAAO,aAAa,eAC3B,OAAO,aAAa,YACrB,OAAO,SAAS,WAAW,WAAW;AAC1C,kBAAM,IAAI,MAAM,gBAAgB;AAAA,UACpC;AAKA,iBAAO,IAAI,IAAI,EAAC,IAAI,MAAK,CAAC;AAAA,QAC9B;AAAA,MACJ;AAKA,UAAI,OAAO,KAAK,UAAU,YAAY;AAClC,aAAK,QAAQ,SAAU,MAAM,SAAS;AAKlC,cAAI;AAEJ,mBAAS,KAAK,QAAQ,KAAK;AAKvB,gBAAI;AACJ,gBAAI;AACJ,gBAAI,QAAQ,OAAO,GAAG;AACtB,gBAAI,SAAS,OAAO,UAAU,UAAU;AACpC,mBAAK,KAAK,OAAO;AACb,oBAAI,OAAO,UAAU,eAAe,KAAK,OAAO,CAAC,GAAG;AAChD,sBAAI,KAAK,OAAO,CAAC;AACjB,sBAAI,MAAM,QAAW;AACjB,0BAAM,CAAC,IAAI;AAAA,kBACf,OAAO;AACH,2BAAO,MAAM,CAAC;AAAA,kBAClB;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ;AACA,mBAAO,QAAQ,KAAK,QAAQ,KAAK,KAAK;AAAA,UAC1C;AAOA,iBAAO,OAAO,IAAI;AAClB,uBAAa,YAAY;AACzB,cAAI,aAAa,KAAK,IAAI,GAAG;AACzB,mBAAO,KAAK,QAAQ,cAAc,SAAU,GAAG;AAC3C,qBAAO,SACE,SAAS,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,MAAM,EAAE;AAAA,YAC5D,CAAC;AAAA,UACL;AAeA,cACI,OAAO;AAAA,YACH,KACK,QAAQ,QAAQ,GAAG,EACnB,QAAQ,UAAU,GAAG,EACrB,QAAQ,SAAS,EAAE;AAAA,UAC5B,GACF;AAOE,gBAAI,KAAK,MAAM,OAAO,GAAG;AAKzB,mBAAQ,OAAO,YAAY,aACrB,KAAK,EAAC,IAAI,EAAC,GAAG,EAAE,IAChB;AAAA,UACV;AAIA,gBAAM,IAAI,YAAY,YAAY;AAAA,QACtC;AAAA,MACJ;AAAA,IACJ,GAAE;AAAA;AAAA;;;AC3fF,IAAAC,iBAAA;AAAA,wCAAAC,UAAAC,SAAA;AAAA,IAAAA,QAAO,UAAU;AAEjB,aAAS,cAAc;AACtB;AACA,aAAO,CAAC;AAAA,IACT;AAAA;AAAA;;;ACLA;AAAA,4CAAAC,UAAAC,SAAA;AAAA,QAAI,SAAS;AAEb,QAAI,WAAW;AACf,QAAI,UAAU,CAAC,gBAA2B;AAE1C,IAAAA,QAAO,UAAU,OAAO,YAAY,UAAU,OAAO;AAAA;AAAA;", "names": ["exports", "module", "create", "F", "trim", "str", "exports", "module", "plugin", "seenPlugin", "exports", "module", "exports", "module", "exports", "module", "exports", "module", "exports", "module", "exports", "module", "exports", "module", "require_json2", "exports", "module", "exports", "module"]}