<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn />
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />
			<cl-flex1 />
			<!-- 关键字搜索 -->
			<cl-search-key />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table">
				<!-- 主题图片 -->
				<template #column-image="{ scope }">
					<el-image
						v-if="scope.row.image"
						:src="scope.row.image"
						style="width: 60px; height: 40px"
						fit="cover"
						preview-teleported
						:preview-src-list="[scope.row.image]"
					/>
					<span v-else>-</span>
				</template>

				<!-- 状态 -->
				<template #column-status="{ scope }">
					<el-tag :type="getStatusType(scope.row.status)">
						{{ getStatusText(scope.row.status) }}
					</el-tag>
				</template>

				<!-- 价格 -->
				<template #column-price="{ scope }">
					<span style="color: #f56c6c; font-weight: bold;">¥{{ scope.row.price }}</span>
				</template>

				<!-- 操作按钮 -->
				<template #slot-btns="{ scope }">
					<el-button-group>
						<!-- <el-button size="small" @click="editTheme(scope.row)">编辑</el-button> -->
						<el-button 
							:type="scope.row.status === 0 ? 'warning' : 'success'"
							@click="toggleStatus(scope.row)"
						>
							{{ scope.row.status === 0 ? '设为维护' : '设为可用' }}
						</el-button>
						<!-- <el-button size="small" type="danger" @click="deleteTheme(scope.row)">删除</el-button> -->
					</el-button-group>
				</template>
			</cl-table>
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert">
			<template #slot-image="{ scope }">
				<cl-upload-space
					:modelValue="scope.image"
					@update:modelValue="scope.image = $event"
					:limit="1"
					accept="image/*"
				/>
			</template>
		</cl-upsert>
	</cl-crud>
</template>

<script lang="ts" setup>
import { useCrud, useTable, useUpsert } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { ElMessage, ElMessageBox } from 'element-plus';

defineOptions({
	name: 'game-theme'
});

const { service } = useCool();

// cl-crud 配置
const Crud = useCrud(
	{
		service: service.game.theme
	},
	(app) => {
		app.refresh();
	}
);

// cl-table 配置
const Table = useTable({
	columns: [
		{
			type: 'selection',
			width: 60
		},
		{
			label: '主题名称',
			prop: 'name',
			minWidth: 150
		},
		{
			label: '主题图片',
			prop: 'image',
			width: 100
		},
		{
			label: '描述',
			prop: 'description',
			minWidth: 200,
			showOverflowTooltip: true
		},
		{
			label: '最少人数',
			prop: 'minPlayers',
			width: 100
		},
		{
			label: '最多人数',
			prop: 'maxPlayers',
			width: 100
		},
		{
			label: '游戏时长',
			prop: 'duration',
			width: 100,
			formatter: (row: any) => `${row.duration}分钟`
		},
		{
			label: '价格',
			prop: 'price',
			width: 100
		},
		{
			label: '状态',
			prop: 'status',
			width: 100
		},
		{
			label: '创建时间',
			prop: 'createTime',
			width: 160,
			sortable: 'desc'
		},
		{
			type: 'op',
			buttons: ['edit','slot-btns','delete'],
			width: 260
		}
	]
});

// cl-upsert 配置
const Upsert = useUpsert({
	items: [
		{
			label: '主题名称',
			prop: 'name',
			required: true,
			component: {
				name: 'el-input',
				props: {
					placeholder: '请输入主题名称'
				}
			}
		},
		{
			label: '主题图片',
			prop: 'image',
			component: {
				name: 'slot-image'
			}
		},
		{
			label: '描述',
			prop: 'description',
			component: {
				name: 'el-input',
				props: {
					type: 'textarea',
					rows: 4,
					placeholder: '请输入主题描述'
				}
			}
		},
		{
			label: '最少人数',
			prop: 'minPlayers',
			required: true,
			component: {
				name: 'el-input-number',
				props: {
					min: 1,
					max: 20,
					placeholder: '最少人数'
				}
			}
		},
		{
			label: '最多人数',
			prop: 'maxPlayers',
			required: true,
			component: {
				name: 'el-input-number',
				props: {
					min: 1,
					max: 20,
					placeholder: '最多人数'
				}
			}
		},
		{
			label: '游戏时长(分钟)',
			prop: 'duration',
			required: true,
			component: {
				name: 'el-input-number',
				props: {
					min: 10,
					max: 300,
					placeholder: '游戏时长'
				}
			}
		},
		{
			label: '价格',
			prop: 'price',
			required: true,
			component: {
				name: 'el-input-number',
				props: {
					min: 0,
					precision: 2,
					placeholder: '价格'
				}
			}
		},
		{
			label: '状态',
			prop: 'status',
			value: 0,
			component: {
				name: 'el-radio-group',
				options: [
					{ label: '可用', value: 0 },
					{ label: '维护中', value: 3 }
				]
			}
		}
	]
});

// 获取状态类型
const getStatusType = (status: number) => {
	const typeMap = {
		0: 'success',    // 空闲
		1: 'warning',    // 使用中
		2: 'info',       // 休息中
		3: 'danger'      // 维护中
	};
	return typeMap[status] || 'info';
};

// 获取状态文本
const getStatusText = (status: number) => {
	const textMap = {
		0: '空闲',
		1: '使用中',
		2: '休息中',
		3: '维护中'
	};
	return textMap[status] || '未知';
};

// 编辑主题
const editTheme = (row: any) => {
	console.log("editSession:",row);
	Upsert.value?.open({
		type: 'update',
		data: row
	});
};

// 切换状态
const toggleStatus = async (row: any) => {
	const newStatus = row.status === 0 ? 3 : 0;
	const statusText = newStatus === 0 ? '可用' : '维护中';
	
	try {
		await ElMessageBox.confirm(
			`确定要将主题"${row.name}"设置为${statusText}状态吗？`,
			'状态切换',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}
		);

		await service.game.theme.setThemeStatus({
			id: row.id,
			status: newStatus
		});

		ElMessage.success('状态更新成功');
		Crud.value?.refresh();
	} catch (error) {
		if (error !== 'cancel') {
			ElMessage.error('状态更新失败');
		}
	}
};

// 删除主题
const deleteTheme = async (row: any) => {
	try {
		await ElMessageBox.confirm(
			`确定要删除主题"${row.name}"吗？此操作不可恢复！`,
			'删除确认',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}
		);

		await service.game.theme.delete({ ids: [row.id] });
		ElMessage.success('删除成功');
		Crud.value?.refresh();
	} catch (error) {
		if (error !== 'cancel') {
			ElMessage.error('删除失败');
		}
	}
};
</script>

<style lang="scss" scoped>
.el-button-group {
	.el-button {
		margin-left: 0;
	}
}
</style>
