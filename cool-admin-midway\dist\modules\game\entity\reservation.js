"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameReservationEntity = void 0;
const base_1 = require("../../base/entity/base");
const typeorm_1 = require("typeorm");
const theme_1 = require("./theme");
const member_1 = require("./member");
/**
 * 游戏预约实体
 */
let GameReservationEntity = class GameReservationEntity extends base_1.BaseEntity {
};
exports.GameReservationEntity = GameReservationEntity;
__decorate([
    (0, typeorm_1.Column)({ comment: '预约号', length: 20, unique: true }),
    __metadata("design:type", String)
], GameReservationEntity.prototype, "reservationNo", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '主题ID' }),
    __metadata("design:type", Number)
], GameReservationEntity.prototype, "themeId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '预约人姓名', length: 50, nullable: true }),
    __metadata("design:type", String)
], GameReservationEntity.prototype, "customerName", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '联系电话', length: 20 }),
    __metadata("design:type", String)
], GameReservationEntity.prototype, "phone", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '预约人数' }),
    __metadata("design:type", Number)
], GameReservationEntity.prototype, "playerCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '预约日期', type: 'date' }),
    __metadata("design:type", Date)
], GameReservationEntity.prototype, "reservationDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '预约开始时间', type: 'datetime' }),
    __metadata("design:type", Date)
], GameReservationEntity.prototype, "startTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '预约结束时间', type: 'datetime' }),
    __metadata("design:type", Date)
], GameReservationEntity.prototype, "endTime", void 0);
__decorate([
    (0, typeorm_1.Column)({
        comment: '预约价格',
        type: 'decimal',
        precision: 10,
        scale: 2, nullable: true
    }),
    __metadata("design:type", Number)
], GameReservationEntity.prototype, "price", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '支付方式 1-现金 2-会员余额 3-第三方券', default: 1 }),
    __metadata("design:type", Number)
], GameReservationEntity.prototype, "paymentType", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '支付状态 0-未支付 1-已支付', default: 0 }),
    __metadata("design:type", Number)
], GameReservationEntity.prototype, "paymentStatus", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '会员ID', nullable: true }),
    __metadata("design:type", Number)
], GameReservationEntity.prototype, "memberId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '第三方券码', nullable: true, length: 100 }),
    __metadata("design:type", String)
], GameReservationEntity.prototype, "couponCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '预约状态 0-待确认 1-已确认 2-已完成 3-已取消', default: 0 }),
    __metadata("design:type", Number)
], GameReservationEntity.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '备注', nullable: true }),
    __metadata("design:type", String)
], GameReservationEntity.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '操作员ID', nullable: true }),
    __metadata("design:type", Number)
], GameReservationEntity.prototype, "operatorId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '取消原因', nullable: true }),
    __metadata("design:type", String)
], GameReservationEntity.prototype, "cancelReason", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '取消时间', type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], GameReservationEntity.prototype, "cancelTime", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => theme_1.GameThemeEntity),
    (0, typeorm_1.JoinColumn)({ name: 'themeId' }),
    __metadata("design:type", theme_1.GameThemeEntity)
], GameReservationEntity.prototype, "theme", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => member_1.GameMemberEntity),
    (0, typeorm_1.JoinColumn)({ name: 'memberId' }),
    __metadata("design:type", member_1.GameMemberEntity)
], GameReservationEntity.prototype, "member", void 0);
exports.GameReservationEntity = GameReservationEntity = __decorate([
    (0, typeorm_1.Entity)('game_reservation')
], GameReservationEntity);
//# sourceMappingURL=data:application/json;base64,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