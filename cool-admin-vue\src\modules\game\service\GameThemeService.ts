import { BaseService } from '/@/cool';

// 游戏主题服务
export default class GameThemeService extends BaseService {
    namespace = '/admin/game/theme';

    // 获取主题状态
    getThemeStatus() {
        return this.request({
            url: '/status',
            method: 'get'
        });
    }

    // 设置主题状态
    setThemeStatus(data: { id: number; status: number }) {
        return this.request({
            url: '/setStatus',
            method: 'post',
            data
        });
    }
}