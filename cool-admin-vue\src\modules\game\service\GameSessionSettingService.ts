import { BaseService } from '/@/cool';

// 场次设置服务
export default class GameSessionSettingService extends BaseService {
	namespace = '/admin/game/sessionSetting';

	// 获取营业时间设置
	getBusinessHours() {
		return this.request({
			url: '/businessHours',
			method: 'get'
		});
	}

	// 设置营业时间
	setBusinessHours(data: {
		dayOfWeek: number; // 0-6 (周日到周六)
		startTime: string;
		endTime: string;
		isActive: boolean;
	}) {
		return this.request({
			url: '/businessHours',
			method: 'post',
			data
		});
	}

	// 获取休息日设置
	getRestDays() {
		return this.request({
			url: '/restDays',
			method: 'get'
		});
	}

	// 设置固定休息日
	setFixedRestDays(data: { dayOfWeek: number[] }) {
		return this.request({
			url: '/fixedRestDays',
			method: 'post',
			data
		});
	}

	// 添加临时休息日
	addTemporaryRestDay(data: {
		date: string;
		reason: string;
		isActive: boolean;
	}) {
		return this.request({
			url: '/temporaryRestDay',
			method: 'post',
			data
		});
	}

	// 删除临时休息日
	removeTemporaryRestDay(data: { id: number }) {
		return this.request({
			url: '/temporaryRestDay',
			method: 'delete',
			data
		});
	}

	// 获取场次参数配置
	getSessionParams() {
		return this.request({
			url: '/sessionParams',
			method: 'get'
		});
	}

	// 设置默认场次参数
	setDefaultSessionParams(data: {
		defaultGameDuration: number; // 默认游戏时长(分钟)
		defaultIntervalTime: number; // 默认间隔时间(分钟)
		maxAdvanceBookingDays: number; // 最大提前预约天数
	}) {
		return this.request({
			url: '/defaultSessionParams',
			method: 'post',
			data
		});
	}

	// 设置主题个性化参数
	setThemeSessionParams(data: {
		themeId: number;
		gameDuration: number;
		intervalTime: number;
		maxPlayers: number;
		pricePerPlayer: number;
	}) {
		return this.request({
			url: '/themeSessionParams',
			method: 'post',
			data
		});
	}

	// 获取主题个性化参数
	getThemeSessionParams(themeId: number) {
		return this.request({
			url: '/themeSessionParams',
			method: 'get',
			params: { themeId }
		});
	}

	// 检查指定日期是否为休息日
	checkRestDay(date: string) {
		return this.request({
			url: '/checkRestDay',
			method: 'get',
			params: { date }
		});
	}
}
