<template>
	<el-scrollbar>
		<div class="game-dashboard">
			<!-- 统计卡片 -->
			<el-row :gutter="20" class="stats-row">
				<el-col :lg="6" :md="12" :xs="24">
					<div class="stat-card">
						<div class="stat-card__icon">
							<el-icon><House /></el-icon>
						</div>
						<div class="stat-card__content">
							<div class="stat-card__title">主题总数</div>
							<div class="stat-card__value">{{ dashboardData.totalThemes }}</div>
						</div>
					</div>
				</el-col>
				<el-col :lg="6" :md="12" :xs="24">
					<div class="stat-card">
						<div class="stat-card__icon active">
							<el-icon><VideoPlay /></el-icon>
						</div>
						<div class="stat-card__content">
							<div class="stat-card__title">进行中游戏</div>
							<div class="stat-card__value">{{ dashboardData.activeGames }}</div>
						</div>
					</div>
				</el-col>
				<el-col :lg="6" :md="12" :xs="24">
					<div class="stat-card">
						<div class="stat-card__icon revenue">
							<el-icon><Money /></el-icon>
						</div>
						<div class="stat-card__content">
							<div class="stat-card__title">今日营收</div>
							<div class="stat-card__value">¥{{ dashboardData.todayRevenue }}</div>
						</div>
					</div>
				</el-col>
				<el-col :lg="6" :md="12" :xs="24">
					<div class="stat-card">
						<div class="stat-card__icon reservation">
							<el-icon><Calendar /></el-icon>
						</div>
						<div class="stat-card__content">
							<div class="stat-card__title">今日预约</div>
							<div class="stat-card__value">{{ dashboardData.todayReservations }}</div>
						</div>
					</div>
				</el-col>
			</el-row>

			<!-- 主题状态看板 -->
			<el-row :gutter="20">
				<el-col :lg="16" :xs="24">
					<el-card class="theme-status-card">
						<template #header>
							<div class="card-header">
								<span>主题状态看板</span>
								<el-button type="primary" size="small" @click="refreshThemeStatus">
									<el-icon><Refresh /></el-icon>
									刷新
								</el-button>
							</div>
						</template>
						<div class="theme-grid">
							<div
								v-for="theme in themeStatusList"
								:key="theme.id"
								class="theme-item"
								:class="getThemeStatusClass(theme.status)"
							>
								<div class="theme-item__header">
									<span class="theme-name">{{ theme.name }}</span>
									<span class="theme-status">{{ getThemeStatusText(theme.status) }}</span>
								</div>
								<div class="theme-item__content">
									<div class="theme-info">
										<span>{{ theme.minPlayers }}-{{ theme.maxPlayers }}人</span>
										<span>{{ theme.duration }}分钟</span>
									</div>
									<div class="theme-price">¥{{ theme.price }}</div>
								</div>
								<div class="theme-item__footer" v-if="theme.currentSession">
									<span>剩余: {{ getRemainingTime(theme.currentSession.endTime) }}</span>
								</div>
							</div>
						</div>
					</el-card>
				</el-col>
				<el-col :lg="8" :xs="24">
					<el-card class="upcoming-card">
						<template #header>
							<span>即将开始的预约</span>
						</template>
						<div class="upcoming-list">
							<div
								v-for="reservation in upcomingReservations"
								:key="reservation.id"
								class="upcoming-item"
							>
								<div class="upcoming-item__time">
									{{ formatTime(reservation.startTime) }}
								</div>
								<div class="upcoming-item__content">
									<div class="upcoming-item__theme">{{ reservation.themeName }}</div>
									<div class="upcoming-item__customer">{{ reservation.customerName }}</div>
									<div class="upcoming-item__players">{{ reservation.playerCount }}人</div>
								</div>
							</div>
						</div>
					</el-card>
				</el-col>
			</el-row>
		</div>
	</el-scrollbar>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { House, VideoPlay, Money, Calendar, Refresh } from '@element-plus/icons-vue';
import { useCool } from '/@/cool';

defineOptions({
	name: 'game-dashboard'
});

const { service } = useCool();

// 类型定义
interface ThemeStatus {
	id: number;
	name: string;
	status: number;
	minPlayers: number;
	maxPlayers: number;
	duration: number;
	price: number;
	currentSession?: {
		endTime: string;
	} | null;
}

interface UpcomingReservation {
	id: number;
	startTime: string;
	themeName: string;
	customerName: string;
	playerCount: number;
}

// 仪表板数据
const dashboardData = reactive({
	totalThemes: 0,
	activeGames: 0,
	todayRevenue: 0,
	todayReservations: 0
});

// 主题状态列表
const themeStatusList = ref<ThemeStatus[]>([]);

// 即将开始的预约
const upcomingReservations = ref<UpcomingReservation[]>([]);

// 获取主题状态样式类
const getThemeStatusClass = (status: number) => {
	const statusMap = {
		0: 'available',    // 空闲
		1: 'occupied',     // 使用中
		2: 'resting',      // 休息中
		3: 'maintenance'   // 维护中
	};
	return statusMap[status] || 'available';
};

// 获取主题状态文本
const getThemeStatusText = (status: number) => {
	const statusMap = {
		0: '空闲',
		1: '使用中',
		2: '休息中',
		3: '维护中'
	};
	return statusMap[status] || '未知';
};

// 获取剩余时间
const getRemainingTime = (endTime: string) => {
	const now = new Date();
	const end = new Date(endTime);
	const diff = end.getTime() - now.getTime();

	if (diff <= 0) return '已结束';

	const minutes = Math.floor(diff / (1000 * 60));
	const hours = Math.floor(minutes / 60);

	if (hours > 0) {
		return `${hours}小时${minutes % 60}分钟`;
	}
	return `${minutes}分钟`;
};

// 格式化时间
const formatTime = (time: string) => {
	return new Date(time).toLocaleTimeString('zh-CN', {
		hour: '2-digit',
		minute: '2-digit'
	});
};

// 刷新主题状态
const refreshThemeStatus = async () => {
	try {
		await loadThemeStatus();
		ElMessage.success('刷新成功');
	} catch (error) {
		ElMessage.error('刷新失败');
	}
};

// 加载仪表板数据
const loadDashboardData = async () => {
	try {
		// 模拟数据，实际应该调用API
		dashboardData.totalThemes = 8;
		dashboardData.activeGames = 3;
		dashboardData.todayRevenue = 2580;
		dashboardData.todayReservations = 12;
	} catch (error) {
		console.error('加载仪表板数据失败:', error);
	}
};

// 加载主题状态
const loadThemeStatus = async () => {
	try {
		// 模拟数据，实际应该调用API
		themeStatusList.value = [
			{
				id: 1,
				name: '密室逃脱A',
				status: 1,
				minPlayers: 2,
				maxPlayers: 6,
				duration: 60,
				price: 168,
				currentSession: {
					endTime: new Date(Date.now() + 25 * 60 * 1000).toISOString()
				}
			},
			{
				id: 2,
				name: '恐怖屋B',
				status: 0,
				minPlayers: 3,
				maxPlayers: 8,
				duration: 45,
				price: 128,
				currentSession: null
			},
			{
				id: 3,
				name: '推理馆C',
				status: 1,
				minPlayers: 4,
				maxPlayers: 6,
				duration: 90,
				price: 198,
				currentSession: {
					endTime: new Date(Date.now() + 45 * 60 * 1000).toISOString()
				}
			},
			{
				id: 4,
				name: '冒险岛D',
				status: 2,
				minPlayers: 2,
				maxPlayers: 4,
				duration: 40,
				price: 98,
				currentSession: null
			},
			{
				id: 5,
				name: '科幻馆E',
				status: 0,
				minPlayers: 3,
				maxPlayers: 7,
				duration: 75,
				price: 158,
				currentSession: null
			},
			{
				id: 6,
				name: '古墓探险F',
				status: 1,
				minPlayers: 2,
				maxPlayers: 5,
				duration: 50,
				price: 138,
				currentSession: {
					endTime: new Date(Date.now() + 15 * 60 * 1000).toISOString()
				}
			}
		];
	} catch (error) {
		console.error('加载主题状态失败:', error);
	}
};

// 加载即将开始的预约
const loadUpcomingReservations = async () => {
	try {
		// 模拟数据，实际应该调用API
		upcomingReservations.value = [
			{
				id: 1,
				startTime: new Date(Date.now() + 30 * 60 * 1000).toISOString(),
				themeName: '密室逃脱A',
				customerName: '张三',
				playerCount: 4
			},
			{
				id: 2,
				startTime: new Date(Date.now() + 60 * 60 * 1000).toISOString(),
				themeName: '恐怖屋B',
				customerName: '李四',
				playerCount: 6
			},
			{
				id: 3,
				startTime: new Date(Date.now() + 90 * 60 * 1000).toISOString(),
				themeName: '推理馆C',
				customerName: '王五',
				playerCount: 5
			},
			{
				id: 4,
				startTime: new Date(Date.now() + 120 * 60 * 1000).toISOString(),
				themeName: '科幻馆E',
				customerName: '赵六',
				playerCount: 3
			}
		];
	} catch (error) {
		console.error('加载预约数据失败:', error);
	}
};

// 初始化数据
onMounted(() => {
	loadDashboardData();
	loadThemeStatus();
	loadUpcomingReservations();

	// 设置定时刷新
	setInterval(() => {
		loadDashboardData();
		loadThemeStatus();
		loadUpcomingReservations();
	}, 30000); // 30秒刷新一次
});
</script>

<style lang="scss" scoped>
.game-dashboard {
	padding: 20px;
	background-color: #f5f7fa;
	min-height: 100vh;

	.stats-row {
		margin-bottom: 20px;
	}

	.stat-card {
		display: flex;
		align-items: center;
		padding: 20px;
		background: white;
		border-radius: 8px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		margin-bottom: 20px;

		&__icon {
			width: 60px;
			height: 60px;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 16px;
			background: #e3f2fd;
			color: #1976d2;

			&.active {
				background: #e8f5e8;
				color: #4caf50;
			}

			&.revenue {
				background: #fff3e0;
				color: #ff9800;
			}

			&.reservation {
				background: #f3e5f5;
				color: #9c27b0;
			}

			.el-icon {
				font-size: 24px;
			}
		}

		&__content {
			flex: 1;
		}

		&__title {
			font-size: 14px;
			color: #666;
			margin-bottom: 8px;
		}

		&__value {
			font-size: 24px;
			font-weight: bold;
			color: #333;
		}
	}

	.theme-status-card {
		margin-bottom: 20px;

		.card-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		.theme-grid {
			display: grid;
			grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
			gap: 16px;
		}

		.theme-item {
			padding: 16px;
			border-radius: 8px;
			border: 2px solid;
			transition: all 0.3s ease;

			&.available {
				border-color: #4caf50;
				background: #e8f5e8;
			}

			&.occupied {
				border-color: #f44336;
				background: #ffebee;
			}

			&.resting {
				border-color: #ff9800;
				background: #fff3e0;
			}

			&.maintenance {
				border-color: #9e9e9e;
				background: #f5f5f5;
			}

			&__header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 12px;

				.theme-name {
					font-weight: bold;
					font-size: 16px;
				}

				.theme-status {
					padding: 4px 8px;
					border-radius: 4px;
					font-size: 12px;
					background: rgba(255, 255, 255, 0.8);
				}
			}

			&__content {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 8px;

				.theme-info {
					display: flex;
					gap: 12px;
					font-size: 14px;
					color: #666;
				}

				.theme-price {
					font-weight: bold;
					color: #f44336;
					font-size: 16px;
				}
			}

			&__footer {
				font-size: 12px;
				color: #666;
				text-align: center;
				padding-top: 8px;
				border-top: 1px solid rgba(0, 0, 0, 0.1);
			}
		}
	}

	.upcoming-card {
		height: fit-content;

		.upcoming-list {
			max-height: 400px;
			overflow-y: auto;
		}

		.upcoming-item {
			display: flex;
			padding: 12px 0;
			border-bottom: 1px solid #eee;

			&:last-child {
				border-bottom: none;
			}

			&__time {
				width: 60px;
				font-weight: bold;
				color: #1976d2;
				font-size: 14px;
			}

			&__content {
				flex: 1;
				margin-left: 12px;
			}

			&__theme {
				font-weight: bold;
				margin-bottom: 4px;
			}

			&__customer {
				font-size: 14px;
				color: #666;
				margin-bottom: 2px;
			}

			&__players {
				font-size: 12px;
				color: #999;
			}
		}
	}
}
</style>
