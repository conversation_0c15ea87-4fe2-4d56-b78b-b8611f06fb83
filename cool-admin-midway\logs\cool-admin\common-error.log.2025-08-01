2025-08-01 22:20:49.886 ERROR 9312 QueryFailedError: Field 'playerName' doesn't have a default value
    at Query.onResult (C:\dev\yh\cool-admin-midway\src\driver\mysql\MysqlQueryRunner.ts:246:33)
    at Query.execute (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\commands\command.js:36:14)
    at PoolConnection.handlePacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:475:34)
    at PacketParser.onPacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:93:12)
    at PacketParser.executeStart (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\packet_parser.js:75:16)
    at Socket.<anonymous> (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:100:25)
    at Socket.emit (node:events:517:28)
    at addChunk (node:internal/streams/readable:368:12)
    at readableAddChunk (node:internal/streams/readable:341:9)
    at Socket.Readable.push (node:internal/streams/readable:278:10) {
  query: 'INSERT INTO `game_session`(`id`, `createTime`, `updateTime`, `tenantId`, `themeId`, `playerName`, `phone`, `playerCount`, `startTime`, `endTime`, `actualPrice`, `paymentType`, `paymentStatus`, `memberId`, `couponCode`, `status`, `entryCode`, `remark`, `operatorId`) VALUES (DEFAULT, ?, ?, DEFAULT, ?, DEFAULT, DEFAULT, ?, ?, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)',
  parameters: [
    '2025-08-01 22:20:49',
    '2025-08-01 22:20:49',
    5,
    2,
    2025-08-02T05:20:39.000Z
  ],
  driverError: Error: Field 'playerName' doesn't have a default value
      at Packet.asError (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\packets\packet.js:740:17)
      at Query.execute (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\commands\command.js:29:26)
      at PoolConnection.handlePacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:475:34)
      at PacketParser.onPacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:93:12)
      at PacketParser.executeStart (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\packet_parser.js:75:16)
      at Socket.<anonymous> (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:100:25)
      at Socket.emit (node:events:517:28)
      at addChunk (node:internal/streams/readable:368:12)
      at readableAddChunk (node:internal/streams/readable:341:9)
      at Socket.Readable.push (node:internal/streams/readable:278:10) {
    code: 'ER_NO_DEFAULT_FOR_FIELD',
    errno: 1364,
    sqlState: 'HY000',
    sqlMessage: "Field 'playerName' doesn't have a default value",
    sql: "INSERT INTO `game_session`(`id`, `createTime`, `updateTime`, `tenantId`, `themeId`, `playerName`, `phone`, `playerCount`, `startTime`, `endTime`, `actualPrice`, `paymentType`, `paymentStatus`, `memberId`, `couponCode`, `status`, `entryCode`, `remark`, `operatorId`) VALUES (DEFAULT, '2025-08-01 22:20:49', '2025-08-01 22:20:49', DEFAULT, 5, DEFAULT, DEFAULT, 2, '2025-08-01 22:20:39.000', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)"
  },
  code: 'ER_NO_DEFAULT_FOR_FIELD',
  errno: 1364,
  sqlState: 'HY000',
  sqlMessage: "Field 'playerName' doesn't have a default value",
  sql: "INSERT INTO `game_session`(`id`, `createTime`, `updateTime`, `tenantId`, `themeId`, `playerName`, `phone`, `playerCount`, `startTime`, `endTime`, `actualPrice`, `paymentType`, `paymentStatus`, `memberId`, `couponCode`, `status`, `entryCode`, `remark`, `operatorId`) VALUES (DEFAULT, '2025-08-01 22:20:49', '2025-08-01 22:20:49', DEFAULT, 5, DEFAULT, DEFAULT, 2, '2025-08-01 22:20:39.000', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)"
}
2025-08-01 22:46:29.994 ERROR 9312 QueryFailedError: Field 'playerName' doesn't have a default value
    at Query.onResult (C:\dev\yh\cool-admin-midway\src\driver\mysql\MysqlQueryRunner.ts:246:33)
    at Query.execute (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\commands\command.js:36:14)
    at PoolConnection.handlePacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:475:34)
    at PacketParser.onPacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:93:12)
    at PacketParser.executeStart (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\packet_parser.js:75:16)
    at Socket.<anonymous> (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:100:25)
    at Socket.emit (node:events:517:28)
    at addChunk (node:internal/streams/readable:368:12)
    at readableAddChunk (node:internal/streams/readable:341:9)
    at Socket.Readable.push (node:internal/streams/readable:278:10) {
  query: 'INSERT INTO `game_session`(`id`, `createTime`, `updateTime`, `tenantId`, `themeId`, `playerName`, `phone`, `playerCount`, `startTime`, `endTime`, `actualPrice`, `paymentType`, `paymentStatus`, `memberId`, `couponCode`, `status`, `entryCode`, `remark`, `operatorId`) VALUES (DEFAULT, ?, ?, DEFAULT, ?, DEFAULT, DEFAULT, ?, ?, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)',
  parameters: [ '2025-08-01 22:46:29', '2025-08-01 22:46:29', 4, 2, Invalid Date ],
  driverError: Error: Field 'playerName' doesn't have a default value
      at Packet.asError (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\packets\packet.js:740:17)
      at Query.execute (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\commands\command.js:29:26)
      at PoolConnection.handlePacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:475:34)
      at PacketParser.onPacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:93:12)
      at PacketParser.executeStart (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\packet_parser.js:75:16)
      at Socket.<anonymous> (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:100:25)
      at Socket.emit (node:events:517:28)
      at addChunk (node:internal/streams/readable:368:12)
      at readableAddChunk (node:internal/streams/readable:341:9)
      at Socket.Readable.push (node:internal/streams/readable:278:10) {
    code: 'ER_NO_DEFAULT_FOR_FIELD',
    errno: 1364,
    sqlState: 'HY000',
    sqlMessage: "Field 'playerName' doesn't have a default value",
    sql: "INSERT INTO `game_session`(`id`, `createTime`, `updateTime`, `tenantId`, `themeId`, `playerName`, `phone`, `playerCount`, `startTime`, `endTime`, `actualPrice`, `paymentType`, `paymentStatus`, `memberId`, `couponCode`, `status`, `entryCode`, `remark`, `operatorId`) VALUES (DEFAULT, '2025-08-01 22:46:29', '2025-08-01 22:46:29', DEFAULT, 4, DEFAULT, DEFAULT, 2, NULL, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)"
  },
  code: 'ER_NO_DEFAULT_FOR_FIELD',
  errno: 1364,
  sqlState: 'HY000',
  sqlMessage: "Field 'playerName' doesn't have a default value",
  sql: "INSERT INTO `game_session`(`id`, `createTime`, `updateTime`, `tenantId`, `themeId`, `playerName`, `phone`, `playerCount`, `startTime`, `endTime`, `actualPrice`, `paymentType`, `paymentStatus`, `memberId`, `couponCode`, `status`, `entryCode`, `remark`, `operatorId`) VALUES (DEFAULT, '2025-08-01 22:46:29', '2025-08-01 22:46:29', DEFAULT, 4, DEFAULT, DEFAULT, 2, NULL, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)"
}
2025-08-01 22:46:45.610 ERROR 9312 QueryFailedError: Column 'startTime' cannot be null
    at Query.onResult (C:\dev\yh\cool-admin-midway\src\driver\mysql\MysqlQueryRunner.ts:246:33)
    at Query.execute (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\commands\command.js:36:14)
    at PoolConnection.handlePacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:475:34)
    at PacketParser.onPacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:93:12)
    at PacketParser.executeStart (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\packet_parser.js:75:16)
    at Socket.<anonymous> (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:100:25)
    at Socket.emit (node:events:517:28)
    at addChunk (node:internal/streams/readable:368:12)
    at readableAddChunk (node:internal/streams/readable:341:9)
    at Socket.Readable.push (node:internal/streams/readable:278:10) {
  query: 'INSERT INTO `game_session`(`id`, `createTime`, `updateTime`, `tenantId`, `themeId`, `playerName`, `phone`, `playerCount`, `startTime`, `endTime`, `actualPrice`, `paymentType`, `paymentStatus`, `memberId`, `couponCode`, `status`, `entryCode`, `remark`, `operatorId`) VALUES (DEFAULT, ?, ?, DEFAULT, ?, DEFAULT, DEFAULT, ?, ?, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)',
  parameters: [ '2025-08-01 22:46:45', '2025-08-01 22:46:45', 4, 2, Invalid Date ],
  driverError: Error: Column 'startTime' cannot be null
      at Packet.asError (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\packets\packet.js:740:17)
      at Query.execute (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\commands\command.js:29:26)
      at PoolConnection.handlePacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:475:34)
      at PacketParser.onPacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:93:12)
      at PacketParser.executeStart (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\packet_parser.js:75:16)
      at Socket.<anonymous> (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:100:25)
      at Socket.emit (node:events:517:28)
      at addChunk (node:internal/streams/readable:368:12)
      at readableAddChunk (node:internal/streams/readable:341:9)
      at Socket.Readable.push (node:internal/streams/readable:278:10) {
    code: 'ER_BAD_NULL_ERROR',
    errno: 1048,
    sqlState: '23000',
    sqlMessage: "Column 'startTime' cannot be null",
    sql: "INSERT INTO `game_session`(`id`, `createTime`, `updateTime`, `tenantId`, `themeId`, `playerName`, `phone`, `playerCount`, `startTime`, `endTime`, `actualPrice`, `paymentType`, `paymentStatus`, `memberId`, `couponCode`, `status`, `entryCode`, `remark`, `operatorId`) VALUES (DEFAULT, '2025-08-01 22:46:45', '2025-08-01 22:46:45', DEFAULT, 4, DEFAULT, DEFAULT, 2, NULL, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)"
  },
  code: 'ER_BAD_NULL_ERROR',
  errno: 1048,
  sqlState: '23000',
  sqlMessage: "Column 'startTime' cannot be null",
  sql: "INSERT INTO `game_session`(`id`, `createTime`, `updateTime`, `tenantId`, `themeId`, `playerName`, `phone`, `playerCount`, `startTime`, `endTime`, `actualPrice`, `paymentType`, `paymentStatus`, `memberId`, `couponCode`, `status`, `entryCode`, `remark`, `operatorId`) VALUES (DEFAULT, '2025-08-01 22:46:45', '2025-08-01 22:46:45', DEFAULT, 4, DEFAULT, DEFAULT, 2, NULL, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)"
}
2025-08-01 22:47:57.709 ERROR 9312 QueryFailedError: Column 'startTime' cannot be null
    at Query.onResult (C:\dev\yh\cool-admin-midway\src\driver\mysql\MysqlQueryRunner.ts:246:33)
    at Query.execute (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\commands\command.js:36:14)
    at PoolConnection.handlePacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:475:34)
    at PacketParser.onPacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:93:12)
    at PacketParser.executeStart (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\packet_parser.js:75:16)
    at Socket.<anonymous> (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:100:25)
    at Socket.emit (node:events:517:28)
    at addChunk (node:internal/streams/readable:368:12)
    at readableAddChunk (node:internal/streams/readable:341:9)
    at Socket.Readable.push (node:internal/streams/readable:278:10) {
  query: 'INSERT INTO `game_session`(`id`, `createTime`, `updateTime`, `tenantId`, `themeId`, `playerName`, `phone`, `playerCount`, `startTime`, `endTime`, `actualPrice`, `paymentType`, `paymentStatus`, `memberId`, `couponCode`, `status`, `entryCode`, `remark`, `operatorId`) VALUES (DEFAULT, ?, ?, DEFAULT, ?, DEFAULT, DEFAULT, ?, ?, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)',
  parameters: [ '2025-08-01 22:47:57', '2025-08-01 22:47:57', 5, 2, Invalid Date ],
  driverError: Error: Column 'startTime' cannot be null
      at Packet.asError (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\packets\packet.js:740:17)
      at Query.execute (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\commands\command.js:29:26)
      at PoolConnection.handlePacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:475:34)
      at PacketParser.onPacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:93:12)
      at PacketParser.executeStart (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\packet_parser.js:75:16)
      at Socket.<anonymous> (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:100:25)
      at Socket.emit (node:events:517:28)
      at addChunk (node:internal/streams/readable:368:12)
      at readableAddChunk (node:internal/streams/readable:341:9)
      at Socket.Readable.push (node:internal/streams/readable:278:10) {
    code: 'ER_BAD_NULL_ERROR',
    errno: 1048,
    sqlState: '23000',
    sqlMessage: "Column 'startTime' cannot be null",
    sql: "INSERT INTO `game_session`(`id`, `createTime`, `updateTime`, `tenantId`, `themeId`, `playerName`, `phone`, `playerCount`, `startTime`, `endTime`, `actualPrice`, `paymentType`, `paymentStatus`, `memberId`, `couponCode`, `status`, `entryCode`, `remark`, `operatorId`) VALUES (DEFAULT, '2025-08-01 22:47:57', '2025-08-01 22:47:57', DEFAULT, 5, DEFAULT, DEFAULT, 2, NULL, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)"
  },
  code: 'ER_BAD_NULL_ERROR',
  errno: 1048,
  sqlState: '23000',
  sqlMessage: "Column 'startTime' cannot be null",
  sql: "INSERT INTO `game_session`(`id`, `createTime`, `updateTime`, `tenantId`, `themeId`, `playerName`, `phone`, `playerCount`, `startTime`, `endTime`, `actualPrice`, `paymentType`, `paymentStatus`, `memberId`, `couponCode`, `status`, `entryCode`, `remark`, `operatorId`) VALUES (DEFAULT, '2025-08-01 22:47:57', '2025-08-01 22:47:57', DEFAULT, 5, DEFAULT, DEFAULT, 2, NULL, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)"
}
2025-08-01 22:50:02.584 ERROR 9312 QueryFailedError: Field 'endTime' doesn't have a default value
    at Query.onResult (C:\dev\yh\cool-admin-midway\src\driver\mysql\MysqlQueryRunner.ts:246:33)
    at Query.execute (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\commands\command.js:36:14)
    at PoolConnection.handlePacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:475:34)
    at PacketParser.onPacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:93:12)
    at PacketParser.executeStart (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\packet_parser.js:75:16)
    at Socket.<anonymous> (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:100:25)
    at Socket.emit (node:events:517:28)
    at addChunk (node:internal/streams/readable:368:12)
    at readableAddChunk (node:internal/streams/readable:341:9)
    at Socket.Readable.push (node:internal/streams/readable:278:10) {
  query: 'INSERT INTO `game_session`(`id`, `createTime`, `updateTime`, `tenantId`, `themeId`, `playerName`, `phone`, `playerCount`, `startTime`, `endTime`, `actualPrice`, `paymentType`, `paymentStatus`, `memberId`, `couponCode`, `status`, `entryCode`, `remark`, `operatorId`) VALUES (DEFAULT, ?, ?, DEFAULT, ?, DEFAULT, DEFAULT, ?, ?, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)',
  parameters: [
    '2025-08-01 22:50:02',
    '2025-08-01 22:50:02',
    3,
    2,
    2025-08-02T05:52:01.000Z
  ],
  driverError: Error: Field 'endTime' doesn't have a default value
      at Packet.asError (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\packets\packet.js:740:17)
      at Query.execute (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\commands\command.js:29:26)
      at PoolConnection.handlePacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:475:34)
      at PacketParser.onPacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:93:12)
      at PacketParser.executeStart (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\packet_parser.js:75:16)
      at Socket.<anonymous> (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:100:25)
      at Socket.emit (node:events:517:28)
      at addChunk (node:internal/streams/readable:368:12)
      at readableAddChunk (node:internal/streams/readable:341:9)
      at Socket.Readable.push (node:internal/streams/readable:278:10) {
    code: 'ER_NO_DEFAULT_FOR_FIELD',
    errno: 1364,
    sqlState: 'HY000',
    sqlMessage: "Field 'endTime' doesn't have a default value",
    sql: "INSERT INTO `game_session`(`id`, `createTime`, `updateTime`, `tenantId`, `themeId`, `playerName`, `phone`, `playerCount`, `startTime`, `endTime`, `actualPrice`, `paymentType`, `paymentStatus`, `memberId`, `couponCode`, `status`, `entryCode`, `remark`, `operatorId`) VALUES (DEFAULT, '2025-08-01 22:50:02', '2025-08-01 22:50:02', DEFAULT, 3, DEFAULT, DEFAULT, 2, '2025-08-01 22:52:01.000', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)"
  },
  code: 'ER_NO_DEFAULT_FOR_FIELD',
  errno: 1364,
  sqlState: 'HY000',
  sqlMessage: "Field 'endTime' doesn't have a default value",
  sql: "INSERT INTO `game_session`(`id`, `createTime`, `updateTime`, `tenantId`, `themeId`, `playerName`, `phone`, `playerCount`, `startTime`, `endTime`, `actualPrice`, `paymentType`, `paymentStatus`, `memberId`, `couponCode`, `status`, `entryCode`, `remark`, `operatorId`) VALUES (DEFAULT, '2025-08-01 22:50:02', '2025-08-01 22:50:02', DEFAULT, 3, DEFAULT, DEFAULT, 2, '2025-08-01 22:52:01.000', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)"
}
2025-08-01 23:23:48.562 ERROR 9312 QueryFailedError: Field 'endTime' doesn't have a default value
    at Query.onResult (C:\dev\yh\cool-admin-midway\src\driver\mysql\MysqlQueryRunner.ts:246:33)
    at Query.execute (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\commands\command.js:36:14)
    at PoolConnection.handlePacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:475:34)
    at PacketParser.onPacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:93:12)
    at PacketParser.executeStart (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\packet_parser.js:75:16)
    at Socket.<anonymous> (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:100:25)
    at Socket.emit (node:events:517:28)
    at addChunk (node:internal/streams/readable:368:12)
    at readableAddChunk (node:internal/streams/readable:341:9)
    at Socket.Readable.push (node:internal/streams/readable:278:10) {
  query: 'INSERT INTO `game_session`(`id`, `createTime`, `updateTime`, `tenantId`, `themeId`, `playerName`, `phone`, `playerCount`, `startTime`, `endTime`, `actualPrice`, `paymentType`, `paymentStatus`, `memberId`, `couponCode`, `status`, `entryCode`, `remark`, `operatorId`) VALUES (DEFAULT, ?, ?, DEFAULT, ?, DEFAULT, DEFAULT, ?, ?, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)',
  parameters: [
    '2025-08-01 23:23:48',
    '2025-08-01 23:23:48',
    5,
    3,
    2025-08-02T06:25:30.000Z
  ],
  driverError: Error: Field 'endTime' doesn't have a default value
      at Packet.asError (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\packets\packet.js:740:17)
      at Query.execute (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\commands\command.js:29:26)
      at PoolConnection.handlePacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:475:34)
      at PacketParser.onPacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:93:12)
      at PacketParser.executeStart (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\packet_parser.js:75:16)
      at Socket.<anonymous> (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:100:25)
      at Socket.emit (node:events:517:28)
      at addChunk (node:internal/streams/readable:368:12)
      at readableAddChunk (node:internal/streams/readable:341:9)
      at Socket.Readable.push (node:internal/streams/readable:278:10) {
    code: 'ER_NO_DEFAULT_FOR_FIELD',
    errno: 1364,
    sqlState: 'HY000',
    sqlMessage: "Field 'endTime' doesn't have a default value",
    sql: "INSERT INTO `game_session`(`id`, `createTime`, `updateTime`, `tenantId`, `themeId`, `playerName`, `phone`, `playerCount`, `startTime`, `endTime`, `actualPrice`, `paymentType`, `paymentStatus`, `memberId`, `couponCode`, `status`, `entryCode`, `remark`, `operatorId`) VALUES (DEFAULT, '2025-08-01 23:23:48', '2025-08-01 23:23:48', DEFAULT, 5, DEFAULT, DEFAULT, 3, '2025-08-01 23:25:30.000', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)"
  },
  code: 'ER_NO_DEFAULT_FOR_FIELD',
  errno: 1364,
  sqlState: 'HY000',
  sqlMessage: "Field 'endTime' doesn't have a default value",
  sql: "INSERT INTO `game_session`(`id`, `createTime`, `updateTime`, `tenantId`, `themeId`, `playerName`, `phone`, `playerCount`, `startTime`, `endTime`, `actualPrice`, `paymentType`, `paymentStatus`, `memberId`, `couponCode`, `status`, `entryCode`, `remark`, `operatorId`) VALUES (DEFAULT, '2025-08-01 23:23:48', '2025-08-01 23:23:48', DEFAULT, 5, DEFAULT, DEFAULT, 3, '2025-08-01 23:25:30.000', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)"
}
2025-08-01 23:25:13.391 ERROR 9312 QueryFailedError: Field 'endTime' doesn't have a default value
    at Query.onResult (C:\dev\yh\cool-admin-midway\src\driver\mysql\MysqlQueryRunner.ts:246:33)
    at Query.execute (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\commands\command.js:36:14)
    at PoolConnection.handlePacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:475:34)
    at PacketParser.onPacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:93:12)
    at PacketParser.executeStart (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\packet_parser.js:75:16)
    at Socket.<anonymous> (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:100:25)
    at Socket.emit (node:events:517:28)
    at addChunk (node:internal/streams/readable:368:12)
    at readableAddChunk (node:internal/streams/readable:341:9)
    at Socket.Readable.push (node:internal/streams/readable:278:10) {
  query: 'INSERT INTO `game_session`(`id`, `createTime`, `updateTime`, `tenantId`, `themeId`, `playerName`, `phone`, `playerCount`, `startTime`, `endTime`, `actualPrice`, `paymentType`, `paymentStatus`, `memberId`, `couponCode`, `status`, `entryCode`, `remark`, `operatorId`) VALUES (DEFAULT, ?, ?, DEFAULT, ?, DEFAULT, DEFAULT, ?, ?, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)',
  parameters: [
    '2025-08-01 23:25:13',
    '2025-08-01 23:25:13',
    5,
    3,
    2025-08-02T06:27:03.000Z
  ],
  driverError: Error: Field 'endTime' doesn't have a default value
      at Packet.asError (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\packets\packet.js:740:17)
      at Query.execute (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\commands\command.js:29:26)
      at PoolConnection.handlePacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:475:34)
      at PacketParser.onPacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:93:12)
      at PacketParser.executeStart (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\packet_parser.js:75:16)
      at Socket.<anonymous> (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:100:25)
      at Socket.emit (node:events:517:28)
      at addChunk (node:internal/streams/readable:368:12)
      at readableAddChunk (node:internal/streams/readable:341:9)
      at Socket.Readable.push (node:internal/streams/readable:278:10) {
    code: 'ER_NO_DEFAULT_FOR_FIELD',
    errno: 1364,
    sqlState: 'HY000',
    sqlMessage: "Field 'endTime' doesn't have a default value",
    sql: "INSERT INTO `game_session`(`id`, `createTime`, `updateTime`, `tenantId`, `themeId`, `playerName`, `phone`, `playerCount`, `startTime`, `endTime`, `actualPrice`, `paymentType`, `paymentStatus`, `memberId`, `couponCode`, `status`, `entryCode`, `remark`, `operatorId`) VALUES (DEFAULT, '2025-08-01 23:25:13', '2025-08-01 23:25:13', DEFAULT, 5, DEFAULT, DEFAULT, 3, '2025-08-01 23:27:03.000', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)"
  },
  code: 'ER_NO_DEFAULT_FOR_FIELD',
  errno: 1364,
  sqlState: 'HY000',
  sqlMessage: "Field 'endTime' doesn't have a default value",
  sql: "INSERT INTO `game_session`(`id`, `createTime`, `updateTime`, `tenantId`, `themeId`, `playerName`, `phone`, `playerCount`, `startTime`, `endTime`, `actualPrice`, `paymentType`, `paymentStatus`, `memberId`, `couponCode`, `status`, `entryCode`, `remark`, `operatorId`) VALUES (DEFAULT, '2025-08-01 23:25:13', '2025-08-01 23:25:13', DEFAULT, 5, DEFAULT, DEFAULT, 3, '2025-08-01 23:27:03.000', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)"
}
2025-08-01 23:28:56.386 ERROR 9312 QueryFailedError: Field 'actualPrice' doesn't have a default value
    at Query.onResult (C:\dev\yh\cool-admin-midway\src\driver\mysql\MysqlQueryRunner.ts:246:33)
    at Query.execute (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\commands\command.js:36:14)
    at PoolConnection.handlePacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:475:34)
    at PacketParser.onPacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:93:12)
    at PacketParser.executeStart (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\packet_parser.js:75:16)
    at Socket.<anonymous> (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:100:25)
    at Socket.emit (node:events:517:28)
    at addChunk (node:internal/streams/readable:368:12)
    at readableAddChunk (node:internal/streams/readable:341:9)
    at Socket.Readable.push (node:internal/streams/readable:278:10) {
  query: 'INSERT INTO `game_session`(`id`, `createTime`, `updateTime`, `tenantId`, `themeId`, `playerName`, `phone`, `playerCount`, `startTime`, `endTime`, `actualPrice`, `paymentType`, `paymentStatus`, `memberId`, `couponCode`, `status`, `entryCode`, `remark`, `operatorId`) VALUES (DEFAULT, ?, ?, DEFAULT, ?, DEFAULT, DEFAULT, ?, ?, ?, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)',
  parameters: [
    '2025-08-01 23:28:56',
    '2025-08-01 23:28:56',
    4,
    5,
    2025-08-02T06:33:43.000Z,
    2025-08-02T07:18:43.000Z
  ],
  driverError: Error: Field 'actualPrice' doesn't have a default value
      at Packet.asError (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\packets\packet.js:740:17)
      at Query.execute (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\commands\command.js:29:26)
      at PoolConnection.handlePacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:475:34)
      at PacketParser.onPacket (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:93:12)
      at PacketParser.executeStart (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\packet_parser.js:75:16)
      at Socket.<anonymous> (C:\dev\yh\cool-admin-midway\node_modules\mysql2\lib\base\connection.js:100:25)
      at Socket.emit (node:events:517:28)
      at addChunk (node:internal/streams/readable:368:12)
      at readableAddChunk (node:internal/streams/readable:341:9)
      at Socket.Readable.push (node:internal/streams/readable:278:10) {
    code: 'ER_NO_DEFAULT_FOR_FIELD',
    errno: 1364,
    sqlState: 'HY000',
    sqlMessage: "Field 'actualPrice' doesn't have a default value",
    sql: "INSERT INTO `game_session`(`id`, `createTime`, `updateTime`, `tenantId`, `themeId`, `playerName`, `phone`, `playerCount`, `startTime`, `endTime`, `actualPrice`, `paymentType`, `paymentStatus`, `memberId`, `couponCode`, `status`, `entryCode`, `remark`, `operatorId`) VALUES (DEFAULT, '2025-08-01 23:28:56', '2025-08-01 23:28:56', DEFAULT, 4, DEFAULT, DEFAULT, 5, '2025-08-01 23:33:43.000', '2025-08-02 00:18:43.000', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)"
  },
  code: 'ER_NO_DEFAULT_FOR_FIELD',
  errno: 1364,
  sqlState: 'HY000',
  sqlMessage: "Field 'actualPrice' doesn't have a default value",
  sql: "INSERT INTO `game_session`(`id`, `createTime`, `updateTime`, `tenantId`, `themeId`, `playerName`, `phone`, `playerCount`, `startTime`, `endTime`, `actualPrice`, `paymentType`, `paymentStatus`, `memberId`, `couponCode`, `status`, `entryCode`, `remark`, `operatorId`) VALUES (DEFAULT, '2025-08-01 23:28:56', '2025-08-01 23:28:56', DEFAULT, 4, DEFAULT, DEFAULT, 5, '2025-08-01 23:33:43.000', '2025-08-02 00:18:43.000', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)"
}
