{"timestamp": "2025-08-02T03:46:48.516Z", "summary": {"total": 65, "passed": 65, "failed": 0, "warnings": 0, "successRate": 100}, "details": [{"type": "passed", "category": "目录结构", "message": "后端游戏模块目录", "details": "", "timestamp": "2025-08-02T03:46:48.463Z"}, {"type": "passed", "category": "目录结构", "message": "后端实体目录", "details": "", "timestamp": "2025-08-02T03:46:48.463Z"}, {"type": "passed", "category": "目录结构", "message": "后端服务目录", "details": "", "timestamp": "2025-08-02T03:46:48.464Z"}, {"type": "passed", "category": "目录结构", "message": "后端控制器目录", "details": "", "timestamp": "2025-08-02T03:46:48.465Z"}, {"type": "passed", "category": "目录结构", "message": "后端DTO目录", "details": "", "timestamp": "2025-08-02T03:46:48.465Z"}, {"type": "passed", "category": "目录结构", "message": "前端游戏模块目录", "details": "", "timestamp": "2025-08-02T03:46:48.466Z"}, {"type": "passed", "category": "目录结构", "message": "前端页面目录", "details": "", "timestamp": "2025-08-02T03:46:48.466Z"}, {"type": "passed", "category": "目录结构", "message": "前端服务目录", "details": "", "timestamp": "2025-08-02T03:46:48.468Z"}, {"type": "passed", "category": "文件检查", "message": "游戏模块配置 存在", "details": "cool-admin-midway/src/modules/game/config.ts", "timestamp": "2025-08-02T03:46:48.469Z"}, {"type": "passed", "category": "文件检查", "message": "主题实体 存在", "details": "cool-admin-midway/src/modules/game/entity/theme.ts", "timestamp": "2025-08-02T03:46:48.470Z"}, {"type": "passed", "category": "文件检查", "message": "场次实体 存在", "details": "cool-admin-midway/src/modules/game/entity/session.ts", "timestamp": "2025-08-02T03:46:48.471Z"}, {"type": "passed", "category": "文件检查", "message": "预约实体 存在", "details": "cool-admin-midway/src/modules/game/entity/reservation.ts", "timestamp": "2025-08-02T03:46:48.472Z"}, {"type": "passed", "category": "文件检查", "message": "会员实体 存在", "details": "cool-admin-midway/src/modules/game/entity/member.ts", "timestamp": "2025-08-02T03:46:48.472Z"}, {"type": "passed", "category": "文件检查", "message": "会员交易实体 存在", "details": "cool-admin-midway/src/modules/game/entity/memberTransaction.ts", "timestamp": "2025-08-02T03:46:48.473Z"}, {"type": "passed", "category": "文件检查", "message": "主题休息时段实体 存在", "details": "cool-admin-midway/src/modules/game/entity/themeRestPeriod.ts", "timestamp": "2025-08-02T03:46:48.474Z"}, {"type": "passed", "category": "文件检查", "message": "优惠券实体 存在", "details": "cool-admin-midway/src/modules/game/entity/coupon.ts", "timestamp": "2025-08-02T03:46:48.475Z"}, {"type": "passed", "category": "文件检查", "message": "打印记录实体 存在", "details": "cool-admin-midway/src/modules/game/entity/printRecord.ts", "timestamp": "2025-08-02T03:46:48.475Z"}, {"type": "passed", "category": "文件检查", "message": "主题服务 存在", "details": "cool-admin-midway/src/modules/game/service/theme.ts", "timestamp": "2025-08-02T03:46:48.476Z"}, {"type": "passed", "category": "文件检查", "message": "场次服务 存在", "details": "cool-admin-midway/src/modules/game/service/session.ts", "timestamp": "2025-08-02T03:46:48.478Z"}, {"type": "passed", "category": "文件检查", "message": "预约服务 存在", "details": "cool-admin-midway/src/modules/game/service/reservation.ts", "timestamp": "2025-08-02T03:46:48.479Z"}, {"type": "passed", "category": "文件检查", "message": "会员服务 存在", "details": "cool-admin-midway/src/modules/game/service/member.ts", "timestamp": "2025-08-02T03:46:48.480Z"}, {"type": "passed", "category": "文件检查", "message": "优惠券服务 存在", "details": "cool-admin-midway/src/modules/game/service/coupon.ts", "timestamp": "2025-08-02T03:46:48.482Z"}, {"type": "passed", "category": "文件检查", "message": "打印服务 存在", "details": "cool-admin-midway/src/modules/game/service/print.ts", "timestamp": "2025-08-02T03:46:48.483Z"}, {"type": "passed", "category": "文件检查", "message": "报表服务 存在", "details": "cool-admin-midway/src/modules/game/service/report.ts", "timestamp": "2025-08-02T03:46:48.484Z"}, {"type": "passed", "category": "文件检查", "message": "主题控制器 存在", "details": "cool-admin-midway/src/modules/game/controller/admin/theme.ts", "timestamp": "2025-08-02T03:46:48.484Z"}, {"type": "passed", "category": "文件检查", "message": "场次控制器 存在", "details": "cool-admin-midway/src/modules/game/controller/admin/session.ts", "timestamp": "2025-08-02T03:46:48.485Z"}, {"type": "passed", "category": "文件检查", "message": "预约控制器 存在", "details": "cool-admin-midway/src/modules/game/controller/admin/reservation.ts", "timestamp": "2025-08-02T03:46:48.486Z"}, {"type": "passed", "category": "文件检查", "message": "会员控制器 存在", "details": "cool-admin-midway/src/modules/game/controller/admin/member.ts", "timestamp": "2025-08-02T03:46:48.487Z"}, {"type": "passed", "category": "文件检查", "message": "仪表板控制器 存在", "details": "cool-admin-midway/src/modules/game/controller/admin/dashboard.ts", "timestamp": "2025-08-02T03:46:48.487Z"}, {"type": "passed", "category": "文件检查", "message": "报表控制器 存在", "details": "cool-admin-midway/src/modules/game/controller/admin/report.ts", "timestamp": "2025-08-02T03:46:48.488Z"}, {"type": "passed", "category": "文件检查", "message": "优惠券控制器 存在", "details": "cool-admin-midway/src/modules/game/controller/admin/coupon.ts", "timestamp": "2025-08-02T03:46:48.489Z"}, {"type": "passed", "category": "文件检查", "message": "主题DTO 存在", "details": "cool-admin-midway/src/modules/game/dto/theme.ts", "timestamp": "2025-08-02T03:46:48.490Z"}, {"type": "passed", "category": "文件检查", "message": "场次DTO 存在", "details": "cool-admin-midway/src/modules/game/dto/session.ts", "timestamp": "2025-08-02T03:46:48.490Z"}, {"type": "passed", "category": "文件检查", "message": "预约DTO 存在", "details": "cool-admin-midway/src/modules/game/dto/reservation.ts", "timestamp": "2025-08-02T03:46:48.491Z"}, {"type": "passed", "category": "文件检查", "message": "会员DTO 存在", "details": "cool-admin-midway/src/modules/game/dto/member.ts", "timestamp": "2025-08-02T03:46:48.491Z"}, {"type": "passed", "category": "文件检查", "message": "优惠券DTO 存在", "details": "cool-admin-midway/src/modules/game/dto/coupon.ts", "timestamp": "2025-08-02T03:46:48.492Z"}, {"type": "passed", "category": "文件检查", "message": "前端游戏模块配置 存在", "details": "cool-admin-vue/src/modules/game/config.ts", "timestamp": "2025-08-02T03:46:48.493Z"}, {"type": "passed", "category": "文件检查", "message": "中文语言包 存在", "details": "cool-admin-vue/src/modules/game/locales/zh-cn.json", "timestamp": "2025-08-02T03:46:48.494Z"}, {"type": "passed", "category": "文件检查", "message": "前端服务层 存在", "details": "cool-admin-vue/src/modules/game/service/index.ts", "timestamp": "2025-08-02T03:46:48.494Z"}, {"type": "passed", "category": "文件检查", "message": "仪表板页面 存在", "details": "cool-admin-vue/src/modules/demo/views/home/<USER>", "timestamp": "2025-08-02T03:46:48.495Z"}, {"type": "passed", "category": "文件检查", "message": "主题管理页面 存在", "details": "cool-admin-vue/src/modules/game/views/theme/index.vue", "timestamp": "2025-08-02T03:46:48.496Z"}, {"type": "passed", "category": "文件检查", "message": "场次管理页面 存在", "details": "cool-admin-vue/src/modules/game/views/session/index.vue", "timestamp": "2025-08-02T03:46:48.496Z"}, {"type": "passed", "category": "文件检查", "message": "预约管理页面 存在", "details": "cool-admin-vue/src/modules/game/views/reservation/index.vue", "timestamp": "2025-08-02T03:46:48.499Z"}, {"type": "passed", "category": "文件检查", "message": "会员管理页面 存在", "details": "cool-admin-vue/src/modules/game/views/member/index.vue", "timestamp": "2025-08-02T03:46:48.500Z"}, {"type": "passed", "category": "文件检查", "message": "优惠券管理页面 存在", "details": "cool-admin-vue/src/modules/game/views/coupon/index.vue", "timestamp": "2025-08-02T03:46:48.500Z"}, {"type": "passed", "category": "文件检查", "message": "报表统计页面 存在", "details": "cool-admin-vue/src/modules/game/views/report/index.vue", "timestamp": "2025-08-02T03:46:48.501Z"}, {"type": "passed", "category": "文件检查", "message": "MVP需求文档 存在", "details": "docs/MVP阶段核心需求清单.md", "timestamp": "2025-08-02T03:46:48.502Z"}, {"type": "passed", "category": "文件检查", "message": "部署测试指南 存在", "details": "docs/游戏管理系统部署和测试指南.md", "timestamp": "2025-08-02T03:46:48.503Z"}, {"type": "passed", "category": "文件检查", "message": "API测试脚本 存在", "details": "cool-admin-vue/src/modules/game/test/api-test.js", "timestamp": "2025-08-02T03:46:48.505Z"}, {"type": "passed", "category": "文件检查", "message": "后端TypeScript配置 存在", "details": "cool-admin-midway/tsconfig.json", "timestamp": "2025-08-02T03:46:48.506Z"}, {"type": "passed", "category": "代码质量", "message": "TypeScript配置正确", "details": "", "timestamp": "2025-08-02T03:46:48.507Z"}, {"type": "passed", "category": "文件检查", "message": "前端TypeScript配置 存在", "details": "cool-admin-vue/tsconfig.json", "timestamp": "2025-08-02T03:46:48.507Z"}, {"type": "passed", "category": "代码质量", "message": "Vue TypeScript配置正确", "details": "", "timestamp": "2025-08-02T03:46:48.508Z"}, {"type": "passed", "category": "文件检查", "message": "后端包配置 存在", "details": "cool-admin-midway/package.json", "timestamp": "2025-08-02T03:46:48.509Z"}, {"type": "passed", "category": "代码质量", "message": "后端依赖配置正确", "details": "", "timestamp": "2025-08-02T03:46:48.509Z"}, {"type": "passed", "category": "文件检查", "message": "前端包配置 存在", "details": "cool-admin-vue/package.json", "timestamp": "2025-08-02T03:46:48.510Z"}, {"type": "passed", "category": "代码质量", "message": "前端依赖配置正确", "details": "", "timestamp": "2025-08-02T03:46:48.510Z"}, {"type": "passed", "category": "MVP需求", "message": "实时仪表板 已实现", "details": "", "timestamp": "2025-08-02T03:46:48.511Z"}, {"type": "passed", "category": "MVP需求", "message": "游戏主题管理 已实现", "details": "", "timestamp": "2025-08-02T03:46:48.511Z"}, {"type": "passed", "category": "MVP需求", "message": "游戏场次管理 已实现", "details": "", "timestamp": "2025-08-02T03:46:48.512Z"}, {"type": "passed", "category": "MVP需求", "message": "预约系统 已实现", "details": "", "timestamp": "2025-08-02T03:46:48.512Z"}, {"type": "passed", "category": "MVP需求", "message": "会员管理 已实现", "details": "", "timestamp": "2025-08-02T03:46:48.512Z"}, {"type": "passed", "category": "MVP需求", "message": "支付验证系统 已实现", "details": "", "timestamp": "2025-08-02T03:46:48.513Z"}, {"type": "passed", "category": "MVP需求", "message": "基础报表 已实现", "details": "", "timestamp": "2025-08-02T03:46:48.514Z"}, {"type": "passed", "category": "MVP需求", "message": "小票打印 已实现", "details": "", "timestamp": "2025-08-02T03:46:48.515Z"}]}