import { Rule, RuleType } from '@midwayjs/validate';

/**
 * 游戏场次创建DTO
 */
export class GameSessionCreateDTO {
  @Rule(RuleType.number().required())
  themeId: number;

  // @Rule(RuleType.string().required().max(50))
  // playerName: string;

  @Rule(RuleType.string().optional().pattern(/^1[3-9]\d{9}$/))
  phone?: string;

  @Rule(RuleType.number().required().min(1).max(20))
  playerCount: number;

  @Rule(RuleType.date().required())
  startTime: Date;

  // @Rule(RuleType.date().required())
  // endTime: Date;

  @Rule(RuleType.number().required().min(0))
  actualPrice: number;

  @Rule(RuleType.number().optional().valid(1, 2, 3))
  paymentType?: number;

  @Rule(RuleType.number().optional())
  memberId?: number;

  @Rule(RuleType.string().optional().max(100))
  couponCode?: string;

  @Rule(RuleType.string().optional())
  remark?: string;
}

/**
 * 游戏场次更新DTO
 */
export class GameSessionUpdateDTO {
  @Rule(RuleType.number().required())
  id: number;

  // @Rule(RuleType.string().optional().max(50))
  // playerName?: string;

  @Rule(RuleType.string().optional().pattern(/^1[3-9]\d{9}$/))
  phone?: string;

  @Rule(RuleType.number().optional().min(1).max(20))
  playerCount?: number;

  @Rule(RuleType.date().optional())
  startTime?: Date;

  @Rule(RuleType.number().optional().min(0))
  actualPrice?: number;

  @Rule(RuleType.number().optional().valid(1, 2, 3))
  paymentType?: number;

  @Rule(RuleType.number().optional().valid(0, 1))
  paymentStatus?: number;

  @Rule(RuleType.number().optional())
  memberId?: number;

  @Rule(RuleType.string().optional().max(100))
  couponCode?: string;

  @Rule(RuleType.number().optional().valid(0, 1, 2, 3))
  status?: number;

  @Rule(RuleType.string().optional())
  remark?: string;
}

/**
 * 开始游戏DTO
 */
export class StartGameDTO {
  @Rule(RuleType.number().required())
  themeId: number;

  // @Rule(RuleType.string().max(50))
  // playerName: string;

  @Rule(RuleType.string().optional().pattern(/^1[3-9]\d{9}$/))
  phone?: string;

  @Rule(RuleType.number().required().min(1).max(20))
  playerCount: number;

  @Rule(RuleType.date().optional())
  startTime?: Date;

  @Rule(RuleType.number().required().valid(1, 2, 3))
  paymentType: number;

  @Rule(RuleType.number().optional())
  memberId?: number;

  @Rule(RuleType.string().optional().max(100))
  couponCode?: string;

  @Rule(RuleType.string().optional())
  remark?: string;
}
