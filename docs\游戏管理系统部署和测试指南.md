# 游戏管理系统部署和测试指南

## 系统概述

基于 cool-admin-vue (前端) 和 cool-admin-midway (后端) 技术栈开发的主题游戏场次管理系统，实现了完整的游戏管理功能。

## 功能模块

### 1. 实时仪表板
- 实时统计数据展示（主题数量、活跃游戏、今日营收、今日预约）
- 主题状态可视化网格（可用、占用、休息、维护）
- 即将开始的预约列表
- 路径：`/` (首页)

### 2. 主题管理
- 主题CRUD操作（增删改查）
- 主题状态管理（可用、占用、休息、维护）
- 主题图片上传
- 价格和时长设置
- 路径：`/game/theme`

### 3. 场次管理
- 游戏场次CRUD操作
- 场次状态管理（待开始、进行中、已完成、已取消）
- 开始/结束/取消游戏操作
- 客户信息管理
- 路径：`/game/session`

### 4. 预约管理
- 预约CRUD操作
- 预约状态管理（待确认、已确认、已完成、已取消）
- 确认/取消预约操作
- 从预约创建场次
- 会员关联功能
- 路径：`/game/reservation`

### 5. 会员管理
- 会员CRUD操作
- 会员余额管理（充值/消费）
- 交易记录查看
- 会员状态管理（正常/禁用）
- 头像上传功能
- 路径：`/game/member`

### 6. 优惠券管理
- 优惠券CRUD操作
- 优惠券验证功能
- 优惠券使用跟踪
- 支持固定金额和百分比折扣
- 使用次数和有效期管理
- 路径：`/game/coupon`

### 7. 报表统计
- 营收报表（总营收、场次数、平均单价）
- 主题统计（受欢迎程度、利用率）
- 会员统计（消费排行、充值统计）
- 图表可视化展示
- 数据导出功能
- 路径：`/game/report`

## 技术架构

### 后端技术栈
- **框架**: Midway.js + Koa.js
- **数据库**: MySQL + TypeORM
- **认证**: JWT
- **文件上传**: 本地存储
- **API文档**: 自动生成

### 前端技术栈
- **框架**: Vue.js 3 + TypeScript
- **UI组件**: Element Plus
- **构建工具**: Vite
- **状态管理**: Pinia
- **图表**: ECharts
- **国际化**: Vue I18n

### 数据库设计
- **GameThemeEntity**: 游戏主题表
- **GameSessionEntity**: 游戏场次表
- **GameReservationEntity**: 游戏预约表
- **GameMemberEntity**: 游戏会员表
- **GameMemberTransactionEntity**: 会员交易记录表
- **GameThemeRestPeriodEntity**: 主题休息时段表
- **GameCouponEntity**: 优惠券表
- **GamePrintRecordEntity**: 打印记录表

## 部署指南

### 环境要求
- Node.js >= 16.0.0
- MySQL >= 8.0
- npm >= 8.0.0

### 后端部署

1. **安装依赖**
```bash
cd cool-admin-midway
npm install
```

2. **配置数据库**
编辑 `src/config/config.local.ts`：
```typescript
export default {
  typeorm: {
    dataSource: {
      default: {
        type: 'mysql',
        host: 'localhost',
        port: 3306,
        username: 'root',
        password: 'your_password',
        database: 'cool_admin',
        synchronize: true,
        logging: false
      }
    }
  }
};
```

3. **启动服务**
```bash
npm run dev
```

服务将在 `http://localhost:8001` 启动

### 前端部署

1. **安装依赖**
```bash
cd cool-admin-vue
npm install
```

2. **配置API地址**
编辑 `src/config/proxy.ts` 确保API代理配置正确

3. **启动服务**
```bash
npm run dev
```

服务将在 `http://localhost:9001` 启动

## 测试指南

### 1. 系统集成测试

#### 访问系统
1. 打开浏览器访问 `http://localhost:9001`
2. 使用管理员账号登录
3. 导航到游戏管理模块

#### API集成测试
1. 打开浏览器开发者工具
2. 在控制台中粘贴并运行 `cool-admin-vue/src/modules/game/test/api-test.js` 中的代码
3. 执行 `gameAPITest.runAllTests()` 运行所有API测试

### 2. 功能测试清单

#### 主题管理测试
- [ ] 创建新主题
- [ ] 编辑主题信息
- [ ] 上传主题图片
- [ ] 修改主题状态
- [ ] 删除主题

#### 场次管理测试
- [ ] 创建新场次
- [ ] 开始游戏
- [ ] 结束游戏
- [ ] 取消游戏
- [ ] 查看场次详情

#### 预约管理测试
- [ ] 创建新预约
- [ ] 确认预约
- [ ] 取消预约
- [ ] 从预约创建场次
- [ ] 关联会员

#### 会员管理测试
- [ ] 注册新会员
- [ ] 会员充值
- [ ] 会员消费
- [ ] 查看交易记录
- [ ] 启用/禁用会员

#### 优惠券管理测试
- [ ] 创建优惠券
- [ ] 验证优惠券
- [ ] 使用优惠券
- [ ] 查看使用记录

#### 报表统计测试
- [ ] 查看营收报表
- [ ] 查看主题统计
- [ ] 查看会员统计
- [ ] 导出报表数据

### 3. 性能测试

#### 数据库性能
- 测试大量数据下的查询性能
- 验证索引优化效果
- 检查事务处理性能

#### 前端性能
- 页面加载速度测试
- 大数据量表格渲染测试
- 图表渲染性能测试

## 常见问题

### 1. 数据库连接失败
- 检查MySQL服务是否启动
- 验证数据库配置信息
- 确认数据库用户权限

### 2. 前端页面空白
- 检查后端服务是否正常运行
- 验证API代理配置
- 查看浏览器控制台错误信息

### 3. 文件上传失败
- 检查上传目录权限
- 验证文件大小限制
- 确认文件类型限制

### 4. 图表不显示
- 检查ECharts依赖是否正确安装
- 验证图表容器尺寸
- 查看控制台错误信息

## 生产环境部署

### 1. 后端生产部署
```bash
# 构建项目
npm run build

# 使用PM2启动
pm2 start dist/bootstrap.js --name game-admin-api

# 配置Nginx反向代理
# /etc/nginx/sites-available/game-admin
server {
    listen 80;
    server_name your-domain.com;
    
    location /api {
        proxy_pass http://localhost:8001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 2. 前端生产部署
```bash
# 构建项目
npm run build

# 部署到Nginx
cp -r dist/* /var/www/html/

# Nginx配置
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/html;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

## 维护指南

### 1. 数据备份
- 定期备份MySQL数据库
- 备份上传的文件资源
- 保存系统配置文件

### 2. 日志监控
- 监控应用错误日志
- 跟踪API访问日志
- 设置性能监控告警

### 3. 安全更新
- 定期更新依赖包
- 检查安全漏洞
- 更新系统补丁

## 联系支持

如有技术问题或需要支持，请联系开发团队。
