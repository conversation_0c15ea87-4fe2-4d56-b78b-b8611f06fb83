2025-08-02 00:14:41.418 INFO 13840 init local task....
2025-08-02 00:15:34.834 INFO 7788 init local task....
2025-08-02 00:43:29.782 ERROR 7788 MidwayValidationError: "themeId" is required
    at ValidateService.validateWithSchema (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\service.js:47:19)
    at ValidateService.validate (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\service.js:31:21)
    at ValidationPipe.validate (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\pipe.js:32:45)
    at ValidationPipe.transform (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\pipe.js:60:21)
    at Object.before (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\decoratorService.js:126:73)
    at AdminGameSessionController.Clz.<computed> (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\aspectService.js:87:21)
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\common\webGenerator.js:43:26
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\info\dist\middleware\info.middleware.js:35:24
    at C:\dev\yh\cool-admin-midway\src\modules\user\middleware\app.ts:63:7
    at C:\dev\yh\cool-admin-midway\src\modules\base\middleware\log.ts:23:7 {
  code: 'VALIDATE_10000',
  cause: [Error [ValidationError]: "themeId" is required] {
    _original: StartGameDTO { id: 1 },
    details: [ [Object] ]
  },
  status: 422
}
2025-08-02 00:44:31.583 ERROR 7788 MidwayValidationError: "playerName" is required
    at ValidateService.validateWithSchema (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\service.js:47:19)
    at ValidateService.validate (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\service.js:31:21)
    at ValidationPipe.validate (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\pipe.js:32:45)
    at ValidationPipe.transform (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\pipe.js:60:21)
    at Object.before (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\decoratorService.js:126:73)
    at AdminGameSessionController.Clz.<computed> (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\aspectService.js:87:21)
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\common\webGenerator.js:43:26
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\info\dist\middleware\info.middleware.js:35:24
    at C:\dev\yh\cool-admin-midway\src\modules\user\middleware\app.ts:63:7
    at C:\dev\yh\cool-admin-midway\src\modules\base\middleware\log.ts:23:7 {
  code: 'VALIDATE_10000',
  cause: [Error [ValidationError]: "playerName" is required] {
    _original: StartGameDTO { id: 1, themeId: 4 },
    details: [ [Object] ]
  },
  status: 422
}
2025-08-02 00:47:02.647 INFO 15172 init local task....
2025-08-02 00:47:03.128 ERROR 15172 MidwayValidationError: "playerCount" is required
    at ValidateService.validateWithSchema (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\service.js:47:19)
    at ValidateService.validate (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\service.js:31:21)
    at ValidationPipe.validate (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\pipe.js:32:45)
    at ValidationPipe.transform (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\pipe.js:60:21)
    at Object.before (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\decoratorService.js:126:73)
    at AdminGameSessionController.Clz.<computed> (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\aspectService.js:87:21)
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\common\webGenerator.js:43:26
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\info\dist\middleware\info.middleware.js:35:24
    at C:\dev\yh\cool-admin-midway\src\modules\user\middleware\app.ts:63:7
    at C:\dev\yh\cool-admin-midway\src\modules\base\middleware\log.ts:23:7 {
  code: 'VALIDATE_10000',
  cause: [Error [ValidationError]: "playerCount" is required] {
    _original: StartGameDTO { id: 1, themeId: 4 },
    details: [ [Object] ]
  },
  status: 422
}
2025-08-02 00:49:42.950 INFO 16200 init local task....
2025-08-02 00:49:58.771 ERROR 16200 MidwayValidationError: "playerCount" is required
    at ValidateService.validateWithSchema (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\service.js:47:19)
    at ValidateService.validate (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\service.js:31:21)
    at ValidationPipe.validate (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\pipe.js:32:45)
    at ValidationPipe.transform (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\pipe.js:60:21)
    at Object.before (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\decoratorService.js:126:73)
    at AdminGameSessionController.Clz.<computed> (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\aspectService.js:87:21)
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\common\webGenerator.js:43:26
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\info\dist\middleware\info.middleware.js:35:24
    at C:\dev\yh\cool-admin-midway\src\modules\user\middleware\app.ts:63:7
    at C:\dev\yh\cool-admin-midway\src\modules\base\middleware\log.ts:23:7 {
  code: 'VALIDATE_10000',
  cause: [Error [ValidationError]: "playerCount" is required] {
    _original: StartGameDTO { id: 1, themeId: 4 },
    details: [ [Object] ]
  },
  status: 422
}
2025-08-02 00:50:18.455 ERROR 16200 MidwayValidationError: "playerName" must be a string
    at ValidateService.validateWithSchema (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\service.js:47:19)
    at ValidateService.validate (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\service.js:31:21)
    at ValidationPipe.validate (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\pipe.js:32:45)
    at ValidationPipe.transform (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\pipe.js:60:21)
    at Object.before (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\decoratorService.js:126:73)
    at AdminGameSessionController.Clz.<computed> (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\aspectService.js:87:21)
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\common\webGenerator.js:43:26
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\info\dist\middleware\info.middleware.js:35:24
    at C:\dev\yh\cool-admin-midway\src\modules\user\middleware\app.ts:63:7
    at C:\dev\yh\cool-admin-midway\src\modules\base\middleware\log.ts:23:7 {
  code: 'VALIDATE_10000',
  cause: [Error [ValidationError]: "playerName" must be a string] {
    _original: StartGameDTO {
      id: 1,
      themeId: 4,
      playerName: null,
      playerCount: 3,
      startTime: '2025-08-02 00:43:40',
      endTime: '2025-08-02 01:28:40',
      paymentType: 1
    },
    details: [ [Object] ]
  },
  status: 422
}
2025-08-02 00:51:04.028 INFO 15704 init local task....
2025-08-02 00:51:25.113 ERROR 15704 MidwayValidationError: "playerName" must be a string
    at ValidateService.validateWithSchema (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\service.js:47:19)
    at ValidateService.validate (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\service.js:31:21)
    at ValidationPipe.validate (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\pipe.js:32:45)
    at ValidationPipe.transform (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\pipe.js:60:21)
    at Object.before (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\decoratorService.js:126:73)
    at AdminGameSessionController.Clz.<computed> (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\aspectService.js:87:21)
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\common\webGenerator.js:43:26
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\info\dist\middleware\info.middleware.js:35:24
    at C:\dev\yh\cool-admin-midway\src\modules\user\middleware\app.ts:63:7
    at C:\dev\yh\cool-admin-midway\src\modules\base\middleware\log.ts:23:7 {
  code: 'VALIDATE_10000',
  cause: [Error [ValidationError]: "playerName" must be a string] {
    _original: StartGameDTO {
      id: 1,
      themeId: 4,
      playerName: null,
      playerCount: 3,
      startTime: '2025-08-02 00:43:40',
      endTime: '2025-08-02 01:28:40',
      paymentType: 1
    },
    details: [ [Object] ]
  },
  status: 422
}
2025-08-02 00:51:50.930 INFO 10548 init local task....
2025-08-02 00:52:12.025 ERROR 10548 MidwayValidationError: "playerName" must be a string
    at ValidateService.validateWithSchema (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\service.js:47:19)
    at ValidateService.validate (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\service.js:31:21)
    at ValidationPipe.validate (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\pipe.js:32:45)
    at ValidationPipe.transform (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\pipe.js:60:21)
    at Object.before (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\decoratorService.js:126:73)
    at AdminGameSessionController.Clz.<computed> (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\aspectService.js:87:21)
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\common\webGenerator.js:43:26
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\info\dist\middleware\info.middleware.js:35:24
    at C:\dev\yh\cool-admin-midway\src\modules\user\middleware\app.ts:63:7
    at C:\dev\yh\cool-admin-midway\src\modules\base\middleware\log.ts:23:7 {
  code: 'VALIDATE_10000',
  cause: [Error [ValidationError]: "playerName" must be a string] {
    _original: StartGameDTO {
      id: 1,
      themeId: 4,
      playerName: null,
      playerCount: 3,
      startTime: '2025-08-02 00:43:40',
      endTime: '2025-08-02 01:28:40',
      paymentType: 1
    },
    details: [ [Object] ]
  },
  status: 422
}
2025-08-02 00:53:03.682 INFO 15656 init local task....
2025-08-02 00:53:19.804 ERROR 15656 MidwayValidationError: "playerName" must be a string
    at ValidateService.validateWithSchema (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\service.js:47:19)
    at ValidateService.validate (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\service.js:31:21)
    at ValidationPipe.validate (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\pipe.js:32:45)
    at ValidationPipe.transform (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\pipe.js:60:21)
    at Object.before (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\decoratorService.js:126:73)
    at AdminGameSessionController.Clz.<computed> (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\aspectService.js:87:21)
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\common\webGenerator.js:43:26
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\info\dist\middleware\info.middleware.js:35:24
    at C:\dev\yh\cool-admin-midway\src\modules\user\middleware\app.ts:63:7
    at C:\dev\yh\cool-admin-midway\src\modules\base\middleware\log.ts:23:7 {
  code: 'VALIDATE_10000',
  cause: [Error [ValidationError]: "playerName" must be a string] {
    _original: StartGameDTO {
      id: 1,
      themeId: 4,
      playerName: null,
      playerCount: 3,
      startTime: '2025-08-02 00:43:40',
      endTime: '2025-08-02 01:28:40',
      paymentType: 1
    },
    details: [ [Object] ]
  },
  status: 422
}
2025-08-02 00:54:09.853 INFO 8928 init local task....
2025-08-02 00:54:22.388 ERROR 8928 MidwayValidationError: "id" is not allowed
    at ValidateService.validateWithSchema (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\service.js:47:19)
    at ValidateService.validate (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\service.js:31:21)
    at ValidationPipe.validate (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\pipe.js:32:45)
    at ValidationPipe.transform (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\pipe.js:60:21)
    at Object.before (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\decoratorService.js:126:73)
    at AdminGameSessionController.Clz.<computed> (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\aspectService.js:87:21)
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\common\webGenerator.js:43:26
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\info\dist\middleware\info.middleware.js:35:24
    at C:\dev\yh\cool-admin-midway\src\modules\user\middleware\app.ts:63:7
    at C:\dev\yh\cool-admin-midway\src\modules\base\middleware\log.ts:23:7 {
  code: 'VALIDATE_10000',
  cause: [Error [ValidationError]: "id" is not allowed] {
    _original: StartGameDTO {
      id: 1,
      themeId: 4,
      playerName: null,
      playerCount: 3,
      startTime: '2025-08-02 00:43:40',
      endTime: '2025-08-02 01:28:40',
      paymentType: 1
    },
    details: [ [Object] ]
  },
  status: 422
}
2025-08-02 00:54:53.170 ERROR 8928 MidwayValidationError: "playerName" is not allowed
    at ValidateService.validateWithSchema (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\service.js:47:19)
    at ValidateService.validate (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\service.js:31:21)
    at ValidationPipe.validate (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\pipe.js:32:45)
    at ValidationPipe.transform (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\pipe.js:60:21)
    at Object.before (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\decoratorService.js:126:73)
    at AdminGameSessionController.Clz.<computed> (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\aspectService.js:87:21)
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\common\webGenerator.js:43:26
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\info\dist\middleware\info.middleware.js:35:24
    at C:\dev\yh\cool-admin-midway\src\modules\user\middleware\app.ts:63:7
    at C:\dev\yh\cool-admin-midway\src\modules\base\middleware\log.ts:23:7 {
  code: 'VALIDATE_10000',
  cause: [Error [ValidationError]: "playerName" is not allowed] {
    _original: StartGameDTO {
      themeId: 4,
      playerName: null,
      playerCount: 3,
      startTime: '2025-08-02 00:43:40',
      endTime: '2025-08-02 01:28:40',
      paymentType: 1
    },
    details: [ [Object] ]
  },
  status: 422
}
2025-08-02 00:55:20.471 ERROR 8928 MidwayValidationError: "endTime" is not allowed
    at ValidateService.validateWithSchema (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\service.js:47:19)
    at ValidateService.validate (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\service.js:31:21)
    at ValidationPipe.validate (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\pipe.js:32:45)
    at ValidationPipe.transform (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\validate\dist\pipe.js:60:21)
    at Object.before (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\decoratorService.js:126:73)
    at AdminGameSessionController.Clz.<computed> (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\aspectService.js:87:21)
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\common\webGenerator.js:43:26
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\info\dist\middleware\info.middleware.js:35:24
    at C:\dev\yh\cool-admin-midway\src\modules\user\middleware\app.ts:63:7
    at C:\dev\yh\cool-admin-midway\src\modules\base\middleware\log.ts:23:7 {
  code: 'VALIDATE_10000',
  cause: [Error [ValidationError]: "endTime" is not allowed] {
    _original: StartGameDTO {
      themeId: 4,
      playerCount: 3,
      startTime: '2025-08-02 00:43:40',
      endTime: '2025-08-02 01:28:40',
      paymentType: 1
    },
    details: [ [Object] ]
  },
  status: 422
}
2025-08-02 00:56:20.123 INFO 9252 init local task....
2025-08-02 00:56:27.331 ERROR 9252 CoolCommException: 人数必须在6-12人之间
    at Object.around (C:\dev\yh\cool-admin-midway\node_modules\@cool-midway\core\dist\decorator\index.js:83:31)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at GameSessionService.Clz.<computed> (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\aspectService.js:89:34)
    at AdminGameSessionController.startGame (C:\dev\yh\cool-admin-midway\src\modules\game\controller\admin\session.ts:36:20)
    at AdminGameSessionController.Clz.<computed> (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\aspectService.js:92:34)
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\common\webGenerator.js:43:26
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\info\dist\middleware\info.middleware.js:35:24
    at C:\dev\yh\cool-admin-midway\src\modules\user\middleware\app.ts:63:7
    at C:\dev\yh\cool-admin-midway\src\modules\base\middleware\log.ts:23:7
    at C:\dev\yh\cool-admin-midway\src\modules\base\middleware\authority.ts:87:15 {
  status: 1001,
  statusCode: undefined
}
2025-08-02 01:27:48.082 ERROR 9252 CoolCommException: 时间段已被占用
    at Object.around (C:\dev\yh\cool-admin-midway\node_modules\@cool-midway\core\dist\decorator\index.js:83:31)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at GameSessionService.Clz.<computed> (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\aspectService.js:89:34)
    at AdminGameSessionController.startGame (C:\dev\yh\cool-admin-midway\src\modules\game\controller\admin\session.ts:36:20)
    at AdminGameSessionController.Clz.<computed> (C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\service\aspectService.js:92:34)
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\core\dist\common\webGenerator.js:43:26
    at C:\dev\yh\cool-admin-midway\node_modules\@midwayjs\info\dist\middleware\info.middleware.js:35:24
    at C:\dev\yh\cool-admin-midway\src\modules\user\middleware\app.ts:63:7
    at C:\dev\yh\cool-admin-midway\src\modules\base\middleware\log.ts:23:7
    at C:\dev\yh\cool-admin-midway\src\modules\base\middleware\authority.ts:87:15 {
  status: 1001,
  statusCode: undefined
}
2025-08-02 01:43:11.912 INFO 12628 init local task....
2025-08-02 02:12:44.167 INFO 9968 init local task....
