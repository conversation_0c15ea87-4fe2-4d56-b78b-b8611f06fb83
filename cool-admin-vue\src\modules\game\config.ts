import { type ModuleConfig } from '/@/cool';
import {GameThemeService} from './service/GameThemeService';
import GameSessionService from './service/GameSessionService';
import GameReservationService from './service/GameReservationService';
import GameMemberService from './service/GameMemberService';
import GameDashboardService from './service/GameDashboardService';
import GameCouponService from './service/GameCouponService';
import GameReportService from './service/GameReportService';


export default (): ModuleConfig => {
	return {
		name: 'game',
		label: '游戏管理',
		description: '主题游戏场次管理系统',
		version: '1.0.0',
		author: 'Game Management Team',
		updateTime: '2024-01-01',

		components: [],
		views: [
			{
				path: '/game/theme',
				meta: {
					label: '主题管理',
					icon: 'icon-game'
				},
				component: () => import('./views/theme/index.vue')
			},
			{
				path: '/game/session',
				meta: {
					label: '场次管理',
					icon: 'icon-time'
				},
				component: () => import('./views/session/index.vue')
			},
			{
				path: '/game/reservation',
				meta: {
					label: '预约管理',
					icon: 'icon-calendar'
				},
				component: () => import('./views/reservation/index.vue')
			},
			{
				path: '/game/member',
				meta: {
					label: '会员管理',
					icon: 'icon-user'
				},
				component: () => import('./views/member/index.vue')
			},
			{
				path: '/game/coupon',
				meta: {
					label: '优惠券管理',
					icon: 'icon-ticket'
				},
				component: () => import('./views/coupon/index.vue')
			},
			{
				path: '/game/report',
				meta: {
					label: '报表统计',
					icon: 'icon-chart'
				},
				component: () => import('./views/report/index.vue')
			}
		],

		// 服务配置
		onLoad({ hasToken }) {
			return hasToken(() => {
				// 注册游戏管理服务
				return {
					service: {
						game: {
							theme: new GameThemeService(),
							session: new GameSessionService(),
							reservation: new GameReservationService(),
							member: new GameMemberService(),
							dashboard: new GameDashboardService(),
							coupon: new GameCouponService(),
							report: new GameReportService()
						}
					}
				};
			});
		}
	};
};
