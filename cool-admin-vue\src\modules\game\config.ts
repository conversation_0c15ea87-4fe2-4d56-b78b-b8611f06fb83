import { type ModuleConfig } from '/@/cool';


export default (): ModuleConfig => {
	return {
		name: 'game',
		label: '游戏管理',
		description: '主题游戏场次管理系统',
		version: '1.0.0',
		author: 'Game Management Team',
		updateTime: '2024-01-01',

		components: [],
		views: [
			{
				path: '/game/theme',
				meta: {
					label: '主题管理',
					icon: 'icon-game'
				},
				component: () => import('./views/theme/index.vue')
			},
			{
				path: '/game/session',
				meta: {
					label: '场次管理',
					icon: 'icon-time'
				},
				component: () => import('./views/session/index.vue')
			},
			{
				path: '/game/reservation',
				meta: {
					label: '预约管理',
					icon: 'icon-calendar'
				},
				component: () => import('./views/reservation/index.vue')
			},
			{
				path: '/game/member',
				meta: {
					label: '会员管理',
					icon: 'icon-user'
				},
				component: () => import('./views/member/index.vue')
			},
			{
				path: '/game/coupon',
				meta: {
					label: '优惠券管理',
					icon: 'icon-ticket'
				},
				component: () => import('./views/coupon/index.vue')
			},
			{
				path: '/game/report',
				meta: {
					label: '报表统计',
					icon: 'icon-chart'
				},
				component: () => import('./views/report/index.vue')
			}
		],

		// 模块初始化
		onLoad({ hasToken }) {
			hasToken(() => {
				// 游戏管理模块初始化逻辑
				// 当用户有token时执行的初始化代码
			});
		}
	};
};
