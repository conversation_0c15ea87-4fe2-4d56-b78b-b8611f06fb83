<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn />
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />
			<cl-flex1 />
			<!-- 验证优惠券 -->
			<el-button type="primary" @click="showVerifyDialog">
				<el-icon><Search /></el-icon>
				验证优惠券
			</el-button>
			<!-- 关键字搜索 -->
			<cl-search-key />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table">
				<!-- 优惠券代码 -->
				<template #column-code="{ scope }">
					<el-tag type="info" style="font-family: monospace;">{{ scope.row.code }}</el-tag>
				</template>

				<!-- 优惠类型 -->
				<template #column-type="{ scope }">
					<el-tag :type="getTypeColor(scope.row.type)">
						{{ getTypeText(scope.row.type) }}
					</el-tag>
				</template>

				<!-- 优惠值 -->
				<template #column-value="{ scope }">
					<span style="color: #f56c6c; font-weight: bold;">
						{{ scope.row.type === 1 ? `¥${scope.row.value}` : `${scope.row.value}%` }}
					</span>
				</template>

				<!-- 状态 -->
				<template #column-status="{ scope }">
					<el-tag :type="getStatusType(scope.row.status)">
						{{ getStatusText(scope.row.status) }}
					</el-tag>
				</template>

				<!-- 有效期 -->
				<template #column-validity="{ scope }">
					<div>
						<div><strong>开始:</strong> {{ formatDate(scope.row.startTime) }}</div>
						<div><strong>结束:</strong> {{ formatDate(scope.row.endTime) }}</div>
					</div>
				</template>

				<!-- 操作按钮 -->
				<template #slot-btns="{ scope }">
					<el-button-group>
						<el-button size="small" @click="editCoupon(scope.row)">编辑</el-button>
						<el-button 
							v-if="scope.row.status === 0"
							size="small" 
							type="success"
							@click="activateCoupon(scope.row)"
						>
							激活
						</el-button>
						<el-button 
							v-if="scope.row.status === 1"
							size="small" 
							type="warning"
							@click="deactivateCoupon(scope.row)"
						>
							停用
						</el-button>
						<el-button size="small" type="danger" @click="deleteCoupon(scope.row)">删除</el-button>
					</el-button-group>
				</template>
			</cl-table>
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert">
			<template #slot-validity="{ scope }">
				<el-date-picker
					v-model="scope.validityRange"
					type="datetimerange"
					range-separator="至"
					start-placeholder="开始时间"
					end-placeholder="结束时间"
					style="width: 100%"
					@change="onValidityChange"
				/>
			</template>
		</cl-upsert>

		<!-- 验证优惠券对话框 -->
		<el-dialog v-model="verifyVisible" title="验证优惠券" width="500px">
			<el-form :model="verifyForm" label-width="100px">
				<el-form-item label="优惠券代码" required>
					<el-input
						v-model="verifyForm.code"
						placeholder="请输入优惠券代码"
						style="width: 100%"
					/>
				</el-form-item>
				<el-form-item label="验证结果" v-if="verifyResult">
					<el-alert
						:title="verifyResult.message"
						:type="verifyResult.valid ? 'success' : 'error'"
						:closable="false"
					/>
					<div v-if="verifyResult.valid && verifyResult.coupon" style="margin-top: 10px;">
						<el-descriptions :column="1" border>
							<el-descriptions-item label="优惠券名称">
								{{ verifyResult.coupon.name }}
							</el-descriptions-item>
							<el-descriptions-item label="优惠类型">
								{{ getTypeText(verifyResult.coupon.type) }}
							</el-descriptions-item>
							<el-descriptions-item label="优惠值">
								{{ verifyResult.coupon.type === 1 ? `¥${verifyResult.coupon.value}` : `${verifyResult.coupon.value}%` }}
							</el-descriptions-item>
							<el-descriptions-item label="有效期">
								{{ formatDate(verifyResult.coupon.startTime) }} 至 {{ formatDate(verifyResult.coupon.endTime) }}
							</el-descriptions-item>
						</el-descriptions>
					</div>
				</el-form-item>
			</el-form>
			<template #footer>
				<el-button @click="verifyVisible = false">关闭</el-button>
				<el-button type="primary" @click="verifyCoupon">验证</el-button>
				<el-button 
					v-if="verifyResult?.valid"
					type="success" 
					@click="useCoupon"
				>
					使用优惠券
				</el-button>
			</template>
		</el-dialog>
	</cl-crud>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { useCrud, useTable, useUpsert } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search } from '@element-plus/icons-vue';

defineOptions({
	name: 'game-coupon'
});

const { service } = useCool();

// 验证对话框
const verifyVisible = ref(false);
const verifyForm = reactive({
	code: ''
});
const verifyResult = ref(null);

// cl-crud 配置
const Crud = useCrud(
	{
		service: service.game.coupon
	},
	(app) => {
		app.refresh();
	}
);

// cl-table 配置
const Table = useTable({
	columns: [
		{
			type: 'selection',
			width: 60
		},
		{
			label: '优惠券名称',
			prop: 'name',
			minWidth: 150
		},
		{
			label: '优惠券代码',
			prop: 'code',
			width: 150
		},
		{
			label: '优惠类型',
			prop: 'type',
			width: 100
		},
		{
			label: '优惠值',
			prop: 'value',
			width: 100
		},
		{
			label: '最低消费',
			prop: 'minAmount',
			width: 100,
			formatter: (row: any) => row.minAmount ? `¥${row.minAmount}` : '无限制'
		},
		{
			label: '使用次数',
			prop: 'usageCount',
			width: 100,
			formatter: (row: any) => `${row.usedCount || 0}/${row.maxUsage || '∞'}`
		},
		{
			label: '有效期',
			prop: 'validity',
			width: 200
		},
		{
			label: '状态',
			prop: 'status',
			width: 100
		},
		{
			label: '创建时间',
			prop: 'createTime',
			width: 160,
			sortable: 'desc'
		},
		{
			type: 'op',
			buttons: ['slot-btns'],
			width: 200
		}
	]
});

// cl-upsert 配置
const Upsert = useUpsert({
	items: [
		{
			label: '优惠券名称',
			prop: 'name',
			required: true,
			component: {
				name: 'el-input',
				props: {
					placeholder: '请输入优惠券名称'
				}
			}
		},
		{
			label: '优惠券代码',
			prop: 'code',
			required: true,
			component: {
				name: 'el-input',
				props: {
					placeholder: '请输入优惠券代码'
				}
			}
		},
		{
			label: '优惠类型',
			prop: 'type',
			required: true,
			value: 1,
			component: {
				name: 'el-radio-group',
				options: [
					{ label: '固定金额', value: 1 },
					{ label: '百分比折扣', value: 2 }
				]
			}
		},
		{
			label: '优惠值',
			prop: 'value',
			required: true,
			component: {
				name: 'el-input-number',
				props: {
					min: 0,
					precision: 2,
					placeholder: '优惠值'
				}
			}
		},
		{
			label: '最低消费金额',
			prop: 'minAmount',
			component: {
				name: 'el-input-number',
				props: {
					min: 0,
					precision: 2,
					placeholder: '最低消费金额（可选）'
				}
			}
		},
		{
			label: '最大使用次数',
			prop: 'maxUsage',
			component: {
				name: 'el-input-number',
				props: {
					min: 1,
					placeholder: '最大使用次数（可选）'
				}
			}
		},
		{
			label: '有效期',
			prop: 'validityRange',
			required: true,
			component: {
				name: 'slot-validity'
			}
		},
		{
			label: '状态',
			prop: 'status',
			value: 1,
			component: {
				name: 'el-radio-group',
				options: [
					{ label: '激活', value: 1 },
					{ label: '停用', value: 0 }
				]
			}
		}
	]
});

// 有效期变化处理
const onValidityChange = (range: any) => {
	if (range && range.length === 2) {
		// 这里可以设置开始和结束时间
		console.log('有效期范围:', range);
	}
};

// 格式化日期
const formatDate = (date: string) => {
	if (!date) return '-';
	return new Date(date).toLocaleDateString('zh-CN');
};

// 获取类型颜色
const getTypeColor = (type: number) => {
	return type === 1 ? 'success' : 'primary';
};

// 获取类型文本
const getTypeText = (type: number) => {
	const textMap = {
		1: '固定金额',
		2: '百分比折扣'
	};
	return textMap[type] || '未知';
};

// 获取状态类型
const getStatusType = (status: number) => {
	const typeMap = {
		0: 'info',       // 未激活
		1: 'success',    // 已激活
		2: 'warning',    // 已过期
		3: 'danger'      // 已用完
	};
	return typeMap[status] || 'info';
};

// 获取状态文本
const getStatusText = (status: number) => {
	const textMap = {
		0: '未激活',
		1: '已激活',
		2: '已过期',
		3: '已用完'
	};
	return textMap[status] || '未知';
};

// 显示验证对话框
const showVerifyDialog = () => {
	verifyForm.code = '';
	verifyResult.value = null;
	verifyVisible.value = true;
};

// 验证优惠券
const verifyCoupon = async () => {
	if (!verifyForm.code.trim()) {
		ElMessage.error('请输入优惠券代码');
		return;
	}

	try {
		const res = await service.game.coupon.verifyCoupon({
			code: verifyForm.code
		});
		verifyResult.value = res;
	} catch (error) {
		verifyResult.value = {
			valid: false,
			message: '验证失败，请检查优惠券代码'
		};
	}
};

// 使用优惠券
const useCoupon = async () => {
	try {
		await ElMessageBox.prompt('请输入场次ID', '使用优惠券', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			inputPattern: /^\d+$/,
			inputErrorMessage: '请输入有效的场次ID'
		}).then(async ({ value }) => {
			await service.game.coupon.useCoupon({
				code: verifyForm.code,
				sessionId: parseInt(value)
			});
			ElMessage.success('优惠券使用成功');
			verifyVisible.value = false;
			Crud.value?.refresh();
		});
	} catch (error) {
		if (error !== 'cancel') {
			ElMessage.error('使用优惠券失败');
		}
	}
};

// 编辑优惠券
const editCoupon = (row: any) => {
	Upsert.value?.open({
		type: 'update',
		data: row
	});
};

// 激活优惠券
const activateCoupon = async (row: any) => {
	try {
		await service.game.coupon.update({
			id: row.id,
			status: 1
		});
		ElMessage.success('优惠券已激活');
		Crud.value?.refresh();
	} catch (error) {
		ElMessage.error('激活失败');
	}
};

// 停用优惠券
const deactivateCoupon = async (row: any) => {
	try {
		await service.game.coupon.update({
			id: row.id,
			status: 0
		});
		ElMessage.success('优惠券已停用');
		Crud.value?.refresh();
	} catch (error) {
		ElMessage.error('停用失败');
	}
};

// 删除优惠券
const deleteCoupon = async (row: any) => {
	try {
		await ElMessageBox.confirm(
			`确定要删除优惠券"${row.name}"吗？此操作不可恢复！`,
			'删除确认',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}
		);

		await service.game.coupon.delete({ ids: [row.id] });
		ElMessage.success('删除成功');
		Crud.value?.refresh();
	} catch (error) {
		if (error !== 'cancel') {
			ElMessage.error('删除失败');
		}
	}
};
</script>

<style lang="scss" scoped>
.el-button-group {
	.el-button {
		margin-left: 0;
	}
}
</style>
