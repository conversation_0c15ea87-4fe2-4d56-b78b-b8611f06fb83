# 智能场次管理系统 - 使用演示

## 快速开始

### 1. 首次配置

#### 步骤1: 配置营业时间
```
导航: 游戏管理 -> 基础设置 -> 营业时间设置

配置示例:
- 周一至周五: 10:00 - 22:00
- 周六至周日: 09:00 - 23:00
- 周二: 休息日 (关闭营业)
```

#### 步骤2: 设置休息日
```
导航: 游戏管理 -> 基础设置 -> 休息日管理

固定休息日: 每周二
临时休息日: 2024-01-01 (元旦), 2024-02-10 (春节)
```

#### 步骤3: 配置场次参数
```
导航: 游戏管理 -> 基础设置 -> 场次参数

默认参数:
- 游戏时长: 90分钟
- 间隔时间: 15分钟
- 最大人数: 6人
```

#### 步骤4: 设置主题参数
```
导航: 游戏管理 -> 基础设置 -> 主题参数

示例配置:
- 密室逃脱A: 90分钟, 间隔15分钟, 6人, ¥88/人
- 密室逃脱B: 120分钟, 间隔20分钟, 8人, ¥128/人
- VR体验: 60分钟, 间隔10分钟, 4人, ¥68/人
```

### 2. 智能预约操作

#### 场景1: 使用场次网格快速预约
```
1. 进入: 游戏管理 -> 预约管理
2. 点击 "新增" 按钮
3. 在智能时间选择器中选择 "场次网格" 模式
4. 选择日期: 今天
5. 选择主题: 密室逃脱A
6. 点击 "生成场次计划" (如果还没有生成)
7. 在网格中点击绿色的可用时段，例如: 14:00-15:30
8. 填写客户信息:
   - 客户姓名: 张三
   - 联系电话: 13800138000
   - 人数: 4
9. 点击确定完成预约
```

#### 场景2: 手动选择时间并处理冲突
```
1. 在智能时间选择器中选择 "手动选择" 模式
2. 选择主题: 密室逃脱B (120分钟)
3. 选择开始时间: 今天 15:00
4. 系统自动计算结束时间: 17:00
5. 如果显示冲突警告:
   - 查看推荐时段: 15:30-17:30, 18:00-20:00
   - 点击推荐时段快速选择
6. 填写客户信息并确认预约
```

### 3. 场次管理操作

#### 生成每日场次计划
```
1. 进入场次网格界面
2. 选择目标日期
3. 选择主题
4. 点击 "生成场次计划"

系统将自动:
- 根据营业时间生成时间段
- 应用主题特定参数
- 避开已有预约时间
- 考虑间隔时间
```

#### 查看场次状态
```
场次网格颜色说明:
🟢 绿色: 可预约 - 点击可快速预约
⚪ 灰色: 已预约 - 悬停查看预约信息
🔵 蓝色: 进行中 - 游戏正在进行
🟠 橙色: 维护中 - 设备维护时段
🔴 红色: 已关闭 - 不可预约时段
```

### 4. 预约管理操作

#### 批量状态更新
```
1. 在预约列表中选择多个预约
2. 点击 "批量设置状态"
3. 选择目标状态: 已确认
4. 填写备注: 批量确认预约
5. 点击确定
```

#### 预约状态流转
```
状态流程:
待确认 -> 已确认 -> 已完成
       -> 已取消

操作方式:
- 单个确认: 点击预约行的 "确认" 按钮
- 单个取消: 点击预约行的 "取消" 按钮
- 批量操作: 选择多行后使用批量功能
```

### 5. 实际使用场景

#### 场景A: 周末高峰期管理
```
问题: 周末预约量大，需要高效安排场次

解决方案:
1. 提前生成周末场次计划
2. 使用场次网格快速查看可用性
3. 通过颜色快速识别状态
4. 利用推荐时段功能处理冲突
5. 批量确认预约提高效率
```

#### 场景B: 设备维护安排
```
问题: 需要安排设备维护时间

解决方案:
1. 在基础设置中添加临时休息日
2. 或在场次网格中手动设置维护状态
3. 系统自动避开维护时段
4. 为受影响客户推荐替代时段
```

#### 场景C: 新主题上线
```
问题: 新主题需要不同的时间参数

解决方案:
1. 在主题管理中添加新主题
2. 在基础设置中配置主题专属参数
3. 生成新主题的场次计划
4. 智能时间选择器自动应用新参数
```

### 6. 高级功能

#### 冲突检测与解决
```
系统自动检测以下冲突:
- 时间重叠冲突
- 营业时间外预约
- 休息日预约
- 设备维护期冲突

解决策略:
- 实时显示冲突警告
- 提供替代时段建议
- 自动计算最近可用时间
- 支持手动调整时间
```

#### 数据导出与分析
```
支持导出:
- 预约明细数据
- 场次利用率统计
- 收入分析报表
- 客户预约历史

导出格式: Excel, CSV, PDF
```

### 7. 移动端适配

#### 响应式设计
```
- 场次网格自适应屏幕尺寸
- 触摸友好的操作界面
- 简化的移动端预约流程
- 快速状态切换功能
```

### 8. 性能优化建议

#### 大数据量处理
```
- 使用分页加载历史数据
- 缓存常用配置信息
- 异步加载场次状态
- 定期清理过期数据
```

#### 实时性保证
```
- WebSocket实时状态同步
- 乐观锁防止并发冲突
- 自动刷新机制
- 离线状态处理
```

### 9. 故障排除

#### 常见问题
```
Q: 场次网格显示空白
A: 检查是否已生成场次计划，确认营业时间配置

Q: 时间冲突检测不准确
A: 验证主题参数配置，检查间隔时间设置

Q: 批量操作失败
A: 确认选择的预约状态允许批量操作

Q: 导出功能异常
A: 检查数据权限，确认导出参数正确
```

### 10. 最佳实践

#### 配置建议
```
1. 营业时间留出缓冲时间
2. 间隔时间考虑清理和准备
3. 定期备份配置数据
4. 建立预约确认流程
5. 设置自动提醒机制
```

#### 操作规范
```
1. 每日检查场次计划
2. 及时处理预约确认
3. 定期更新主题参数
4. 监控系统性能指标
5. 收集用户反馈优化
```

## 技术支持

如有问题，请参考:
- 系统日志: `/logs/game-module.log`
- API文档: `/swagger-ui`
- 测试页面: 游戏管理 -> 场次管理测试
- 开发文档: `README.md`
