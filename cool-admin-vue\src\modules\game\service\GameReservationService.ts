import { BaseService } from '/@/cool';

// 游戏预约服务
export default class GameReservationService extends BaseService {
	namespace = '/admin/game/reservation';

	// 获取即将开始的预约
	getUpcomingReservations() {
		return this.request({
			url: '/upcoming',
			method: 'get'
		});
	}

	// 确认预约
	confirmReservation(data: { id: number }) {
		return this.request({
			url: '/confirm',
			method: 'post',
			data
		});
	}

	// 取消预约
	cancelReservation(data: { id: number }) {
		return this.request({
			url: '/cancel',
			method: 'post',
			data
		});
	}

	// 批量更新状态
	batchUpdateStatus(data: { ids: number[]; status: number; remark?: string }) {
		return this.request({
			url: '/batchUpdateStatus',
			method: 'post',
			data
		});
	}

	// 导出预约数据
	export(params?: any) {
		return this.request({
			url: '/export',
			method: 'get',
			params,
			responseType: 'blob'
		});
	}
}
