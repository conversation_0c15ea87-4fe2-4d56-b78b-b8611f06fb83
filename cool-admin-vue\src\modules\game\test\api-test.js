// 游戏管理系统API测试脚本
// 在浏览器控制台中运行此脚本来测试API集成

console.log('开始测试游戏管理系统API...');

// 测试主题管理API
async function testThemeAPI() {
    console.log('\n=== 测试主题管理API ===');
    
    try {
        // 获取主题列表
        const response = await fetch('/api/admin/game/theme/list', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({
                page: 1,
                size: 10
            })
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ 主题列表获取成功:', data);
        } else {
            console.log('❌ 主题列表获取失败:', response.status);
        }
    } catch (error) {
        console.log('❌ 主题API测试出错:', error);
    }
}

// 测试场次管理API
async function testSessionAPI() {
    console.log('\n=== 测试场次管理API ===');
    
    try {
        const response = await fetch('/api/admin/game/session/list', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({
                page: 1,
                size: 10
            })
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ 场次列表获取成功:', data);
        } else {
            console.log('❌ 场次列表获取失败:', response.status);
        }
    } catch (error) {
        console.log('❌ 场次API测试出错:', error);
    }
}

// 测试预约管理API
async function testReservationAPI() {
    console.log('\n=== 测试预约管理API ===');
    
    try {
        const response = await fetch('/api/admin/game/reservation/list', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({
                page: 1,
                size: 10
            })
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ 预约列表获取成功:', data);
        } else {
            console.log('❌ 预约列表获取失败:', response.status);
        }
    } catch (error) {
        console.log('❌ 预约API测试出错:', error);
    }
}

// 测试会员管理API
async function testMemberAPI() {
    console.log('\n=== 测试会员管理API ===');
    
    try {
        const response = await fetch('/api/admin/game/member/list', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({
                page: 1,
                size: 10
            })
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ 会员列表获取成功:', data);
        } else {
            console.log('❌ 会员列表获取失败:', response.status);
        }
    } catch (error) {
        console.log('❌ 会员API测试出错:', error);
    }
}

// 测试仪表板API
async function testDashboardAPI() {
    console.log('\n=== 测试仪表板API ===');
    
    try {
        const response = await fetch('/api/admin/game/dashboard/data', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ 仪表板数据获取成功:', data);
        } else {
            console.log('❌ 仪表板数据获取失败:', response.status);
        }
    } catch (error) {
        console.log('❌ 仪表板API测试出错:', error);
    }
}

// 运行所有测试
async function runAllTests() {
    console.log('🚀 开始运行游戏管理系统API测试...\n');
    
    await testThemeAPI();
    await testSessionAPI();
    await testReservationAPI();
    await testMemberAPI();
    await testDashboardAPI();
    
    console.log('\n✨ API测试完成！');
}

// 导出测试函数
window.gameAPITest = {
    runAllTests,
    testThemeAPI,
    testSessionAPI,
    testReservationAPI,
    testMemberAPI,
    testDashboardAPI
};

console.log('📋 API测试脚本已加载！');
console.log('💡 使用方法：');
console.log('   - 运行所有测试: gameAPITest.runAllTests()');
console.log('   - 单独测试主题API: gameAPITest.testThemeAPI()');
console.log('   - 单独测试场次API: gameAPITest.testSessionAPI()');
console.log('   - 单独测试预约API: gameAPITest.testReservationAPI()');
console.log('   - 单独测试会员API: gameAPITest.testMemberAPI()');
console.log('   - 单独测试仪表板API: gameAPITest.testDashboardAPI()');
