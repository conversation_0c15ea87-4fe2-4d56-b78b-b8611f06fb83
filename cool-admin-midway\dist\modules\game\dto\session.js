"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StartGameDTO = exports.GameSessionUpdateDTO = exports.GameSessionCreateDTO = void 0;
const validate_1 = require("@midwayjs/validate");
/**
 * 游戏场次创建DTO
 */
class GameSessionCreateDTO {
}
exports.GameSessionCreateDTO = GameSessionCreateDTO;
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.number().required()),
    __metadata("design:type", Number)
], GameSessionCreateDTO.prototype, "themeId", void 0);
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.string().optional().pattern(/^1[3-9]\d{9}$/)),
    __metadata("design:type", String)
], GameSessionCreateDTO.prototype, "phone", void 0);
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.number().required().min(1).max(20)),
    __metadata("design:type", Number)
], GameSessionCreateDTO.prototype, "playerCount", void 0);
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.date().required()),
    __metadata("design:type", Date)
], GameSessionCreateDTO.prototype, "startTime", void 0);
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.number().required().min(0)),
    __metadata("design:type", Number)
], GameSessionCreateDTO.prototype, "actualPrice", void 0);
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.number().optional().valid(1, 2, 3)),
    __metadata("design:type", Number)
], GameSessionCreateDTO.prototype, "paymentType", void 0);
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.number().optional()),
    __metadata("design:type", Number)
], GameSessionCreateDTO.prototype, "memberId", void 0);
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.string().optional().max(100)),
    __metadata("design:type", String)
], GameSessionCreateDTO.prototype, "couponCode", void 0);
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.string().optional()),
    __metadata("design:type", String)
], GameSessionCreateDTO.prototype, "remark", void 0);
/**
 * 游戏场次更新DTO
 */
class GameSessionUpdateDTO {
}
exports.GameSessionUpdateDTO = GameSessionUpdateDTO;
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.number().required()),
    __metadata("design:type", Number)
], GameSessionUpdateDTO.prototype, "id", void 0);
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.string().optional().pattern(/^1[3-9]\d{9}$/)),
    __metadata("design:type", String)
], GameSessionUpdateDTO.prototype, "phone", void 0);
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.number().optional().min(1).max(20)),
    __metadata("design:type", Number)
], GameSessionUpdateDTO.prototype, "playerCount", void 0);
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.date().optional()),
    __metadata("design:type", Date)
], GameSessionUpdateDTO.prototype, "startTime", void 0);
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.number().optional().min(0)),
    __metadata("design:type", Number)
], GameSessionUpdateDTO.prototype, "actualPrice", void 0);
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.number().optional().valid(1, 2, 3)),
    __metadata("design:type", Number)
], GameSessionUpdateDTO.prototype, "paymentType", void 0);
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.number().optional().valid(0, 1)),
    __metadata("design:type", Number)
], GameSessionUpdateDTO.prototype, "paymentStatus", void 0);
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.number().optional()),
    __metadata("design:type", Number)
], GameSessionUpdateDTO.prototype, "memberId", void 0);
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.string().optional().max(100)),
    __metadata("design:type", String)
], GameSessionUpdateDTO.prototype, "couponCode", void 0);
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.number().optional().valid(0, 1, 2, 3)),
    __metadata("design:type", Number)
], GameSessionUpdateDTO.prototype, "status", void 0);
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.string().optional()),
    __metadata("design:type", String)
], GameSessionUpdateDTO.prototype, "remark", void 0);
/**
 * 开始游戏DTO
 */
class StartGameDTO {
}
exports.StartGameDTO = StartGameDTO;
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.number().required()),
    __metadata("design:type", Number)
], StartGameDTO.prototype, "themeId", void 0);
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.string().optional().pattern(/^1[3-9]\d{9}$/)),
    __metadata("design:type", String)
], StartGameDTO.prototype, "phone", void 0);
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.number().required().min(1).max(20)),
    __metadata("design:type", Number)
], StartGameDTO.prototype, "playerCount", void 0);
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.date().optional()),
    __metadata("design:type", Date)
], StartGameDTO.prototype, "startTime", void 0);
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.number().required().valid(1, 2, 3)),
    __metadata("design:type", Number)
], StartGameDTO.prototype, "paymentType", void 0);
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.number().optional()),
    __metadata("design:type", Number)
], StartGameDTO.prototype, "memberId", void 0);
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.string().optional().max(100)),
    __metadata("design:type", String)
], StartGameDTO.prototype, "couponCode", void 0);
__decorate([
    (0, validate_1.Rule)(validate_1.RuleType.string().optional()),
    __metadata("design:type", String)
], StartGameDTO.prototype, "remark", void 0);
//# sourceMappingURL=data:application/json;base64,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