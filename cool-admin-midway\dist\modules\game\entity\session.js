"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameSessionEntity = void 0;
const base_1 = require("../../base/entity/base");
const typeorm_1 = require("typeorm");
const theme_1 = require("./theme");
const member_1 = require("./member");
/**
 * 游戏场次实体
 */
let GameSessionEntity = class GameSessionEntity extends base_1.BaseEntity {
};
exports.GameSessionEntity = GameSessionEntity;
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '主题ID' }),
    __metadata("design:type", Number)
], GameSessionEntity.prototype, "themeId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '玩家姓名（匿名处理）', length: 50, nullable: true }),
    __metadata("design:type", String)
], GameSessionEntity.prototype, "playerName", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '联系电话', length: 20, nullable: true }),
    __metadata("design:type", String)
], GameSessionEntity.prototype, "phone", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '游戏人数' }),
    __metadata("design:type", Number)
], GameSessionEntity.prototype, "playerCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '开始时间', type: 'datetime' }),
    __metadata("design:type", Date)
], GameSessionEntity.prototype, "startTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '结束时间', type: 'datetime' }),
    __metadata("design:type", Date)
], GameSessionEntity.prototype, "endTime", void 0);
__decorate([
    (0, typeorm_1.Column)({
        comment: '实际价格',
        type: 'decimal',
        precision: 10,
        scale: 2, nullable: true
    }),
    __metadata("design:type", Number)
], GameSessionEntity.prototype, "actualPrice", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '支付方式 1-现金 2-会员余额 3-第三方券', default: 1 }),
    __metadata("design:type", Number)
], GameSessionEntity.prototype, "paymentType", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '支付状态 0-未支付 1-已支付', default: 0 }),
    __metadata("design:type", Number)
], GameSessionEntity.prototype, "paymentStatus", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '会员ID', nullable: true }),
    __metadata("design:type", Number)
], GameSessionEntity.prototype, "memberId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '第三方券码', nullable: true, length: 100 }),
    __metadata("design:type", String)
], GameSessionEntity.prototype, "couponCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '游戏状态 0-待开始 1-进行中 2-已结束 3-已取消', default: 0 }),
    __metadata("design:type", Number)
], GameSessionEntity.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '入场码', length: 20, nullable: true }),
    __metadata("design:type", String)
], GameSessionEntity.prototype, "entryCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '备注', nullable: true }),
    __metadata("design:type", String)
], GameSessionEntity.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '操作员ID', nullable: true }),
    __metadata("design:type", Number)
], GameSessionEntity.prototype, "operatorId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => theme_1.GameThemeEntity),
    (0, typeorm_1.JoinColumn)({ name: 'themeId' }),
    __metadata("design:type", theme_1.GameThemeEntity)
], GameSessionEntity.prototype, "theme", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => member_1.GameMemberEntity),
    (0, typeorm_1.JoinColumn)({ name: 'memberId' }),
    __metadata("design:type", member_1.GameMemberEntity)
], GameSessionEntity.prototype, "member", void 0);
exports.GameSessionEntity = GameSessionEntity = __decorate([
    (0, typeorm_1.Entity)('game_session')
], GameSessionEntity);
//# sourceMappingURL=data:application/json;base64,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