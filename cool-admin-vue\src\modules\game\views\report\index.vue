<template>
	<div class="game-report">
		<!-- 筛选条件 -->
		<el-card class="filter-card" shadow="never">
			<el-form :model="filterForm" inline>
				<el-form-item label="时间范围">
					<el-date-picker
						v-model="filterForm.dateRange"
						type="daterange"
						range-separator="至"
						start-placeholder="开始日期"
						end-placeholder="结束日期"
						style="width: 300px"
					/>
				</el-form-item>
				<el-form-item label="报表类型">
					<el-select v-model="filterForm.reportType" style="width: 150px">
						<el-option label="营收报表" value="revenue" />
						<el-option label="主题统计" value="theme" />
						<el-option label="会员统计" value="member" />
					</el-select>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" @click="loadReport">查询</el-button>
					<el-button @click="exportReport">导出</el-button>
				</el-form-item>
			</el-form>
		</el-card>

		<!-- 营收报表 -->
		<div v-if="filterForm.reportType === 'revenue'">
			<el-row :gutter="20">
				<el-col :span="6">
					<el-card class="stat-card">
						<div class="stat-item">
							<div class="stat-value">¥{{ revenueData.totalRevenue }}</div>
							<div class="stat-label">总营收</div>
						</div>
					</el-card>
				</el-col>
				<el-col :span="6">
					<el-card class="stat-card">
						<div class="stat-item">
							<div class="stat-value">{{ revenueData.totalSessions }}</div>
							<div class="stat-label">总场次</div>
						</div>
					</el-card>
				</el-col>
				<el-col :span="6">
					<el-card class="stat-card">
						<div class="stat-item">
							<div class="stat-value">¥{{ revenueData.avgRevenue }}</div>
							<div class="stat-label">平均单价</div>
						</div>
					</el-card>
				</el-col>
				<el-col :span="6">
					<el-card class="stat-card">
						<div class="stat-item">
							<div class="stat-value">{{ revenueData.totalCustomers }}</div>
							<div class="stat-label">总客户数</div>
						</div>
					</el-card>
				</el-col>
			</el-row>

			<el-card style="margin-top: 20px;">
				<template #header>
					<span>营收趋势图</span>
				</template>
				<div ref="revenueChart" style="height: 400px;"></div>
			</el-card>

			<el-card style="margin-top: 20px;">
				<template #header>
					<span>营收明细</span>
				</template>
				<el-table :data="revenueDetails" style="width: 100%">
					<el-table-column prop="date" label="日期" width="120" />
					<el-table-column prop="sessions" label="场次数" width="100" />
					<el-table-column prop="revenue" label="营收" width="120">
						<template #default="{ row }">
							<span style="color: #67c23a; font-weight: bold;">¥{{ row.revenue }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="customers" label="客户数" width="100" />
					<el-table-column prop="avgPrice" label="平均单价" width="120">
						<template #default="{ row }">
							¥{{ row.avgPrice }}
						</template>
					</el-table-column>
				</el-table>
			</el-card>
		</div>

		<!-- 主题统计 -->
		<div v-if="filterForm.reportType === 'theme'">
			<el-card>
				<template #header>
					<span>主题受欢迎程度</span>
				</template>
				<div ref="themeChart" style="height: 400px;"></div>
			</el-card>

			<el-card style="margin-top: 20px;">
				<template #header>
					<span>主题统计详情</span>
				</template>
				<el-table :data="themeStats" style="width: 100%">
					<el-table-column prop="themeName" label="主题名称" min-width="150" />
					<el-table-column prop="sessions" label="场次数" width="100" />
					<el-table-column prop="revenue" label="营收" width="120">
						<template #default="{ row }">
							<span style="color: #67c23a; font-weight: bold;">¥{{ row.revenue }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="customers" label="客户数" width="100" />
					<el-table-column prop="avgRating" label="平均评分" width="100">
						<template #default="{ row }">
							<el-rate v-model="row.avgRating" disabled show-score />
						</template>
					</el-table-column>
					<el-table-column prop="utilizationRate" label="利用率" width="100">
						<template #default="{ row }">
							{{ row.utilizationRate }}%
						</template>
					</el-table-column>
				</el-table>
			</el-card>
		</div>

		<!-- 会员统计 -->
		<div v-if="filterForm.reportType === 'member'">
			<el-row :gutter="20">
				<el-col :span="6">
					<el-card class="stat-card">
						<div class="stat-item">
							<div class="stat-value">{{ memberData.totalMembers }}</div>
							<div class="stat-label">总会员数</div>
						</div>
					</el-card>
				</el-col>
				<el-col :span="6">
					<el-card class="stat-card">
						<div class="stat-item">
							<div class="stat-value">{{ memberData.newMembers }}</div>
							<div class="stat-label">新增会员</div>
						</div>
					</el-card>
				</el-col>
				<el-col :span="6">
					<el-card class="stat-card">
						<div class="stat-item">
							<div class="stat-value">¥{{ memberData.totalRecharge }}</div>
							<div class="stat-label">总充值</div>
						</div>
					</el-card>
				</el-col>
				<el-col :span="6">
					<el-card class="stat-card">
						<div class="stat-item">
							<div class="stat-value">¥{{ memberData.totalConsume }}</div>
							<div class="stat-label">总消费</div>
						</div>
					</el-card>
				</el-col>
			</el-row>

			<el-card style="margin-top: 20px;">
				<template #header>
					<span>会员消费分布</span>
				</template>
				<div ref="memberChart" style="height: 400px;"></div>
			</el-card>

			<el-card style="margin-top: 20px;">
				<template #header>
					<span>会员排行榜</span>
				</template>
				<el-table :data="memberRanking" style="width: 100%">
					<el-table-column type="index" label="排名" width="80" />
					<el-table-column prop="memberName" label="会员姓名" width="120" />
					<el-table-column prop="phone" label="电话" width="130" />
					<el-table-column prop="totalConsume" label="总消费" width="120">
						<template #default="{ row }">
							<span style="color: #f56c6c; font-weight: bold;">¥{{ row.totalConsume }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="sessions" label="游戏次数" width="100" />
					<el-table-column prop="lastVisit" label="最后访问" width="160">
						<template #default="{ row }">
							{{ formatTime(row.lastVisit) }}
						</template>
					</el-table-column>
				</el-table>
			</el-card>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import { useCool } from '/@/cool';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts';

defineOptions({
	name: 'game-report'
});

const { service } = useCool();

// 筛选表单
const filterForm = reactive({
	dateRange: [
		new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
		new Date()
	],
	reportType: 'revenue'
});

// 营收数据
const revenueData = reactive({
	totalRevenue: 0,
	totalSessions: 0,
	avgRevenue: 0,
	totalCustomers: 0
});

const revenueDetails = ref([]);

// 主题统计数据
const themeStats = ref([]);

// 会员数据
const memberData = reactive({
	totalMembers: 0,
	newMembers: 0,
	totalRecharge: 0,
	totalConsume: 0
});

const memberRanking = ref([]);

// 图表引用
const revenueChart = ref();
const themeChart = ref();
const memberChart = ref();

// 图表实例
let revenueChartInstance: any = null;
let themeChartInstance: any = null;
let memberChartInstance: any = null;

// 加载报表数据
const loadReport = async () => {
	if (!filterForm.dateRange || filterForm.dateRange.length !== 2) {
		ElMessage.error('请选择时间范围');
		return;
	}

	const [startDate, endDate] = filterForm.dateRange;
	const params = {
		startDate: startDate.toISOString().split('T')[0],
		endDate: endDate.toISOString().split('T')[0]
	};

	try {
		switch (filterForm.reportType) {
			case 'revenue':
				await loadRevenueReport(params);
				break;
			case 'theme':
				await loadThemeReport(params);
				break;
			case 'member':
				await loadMemberReport(params);
				break;
		}
	} catch (error) {
		ElMessage.error('加载报表数据失败');
	}
};

// 加载营收报表
const loadRevenueReport = async (params: any) => {
	// 模拟数据
	revenueData.totalRevenue = 25800;
	revenueData.totalSessions = 156;
	revenueData.avgRevenue = 165;
	revenueData.totalCustomers = 89;

	revenueDetails.value = [
		{ date: '2024-01-01', sessions: 12, revenue: 1980, customers: 8, avgPrice: 165 },
		{ date: '2024-01-02', sessions: 15, revenue: 2475, customers: 12, avgPrice: 165 },
		{ date: '2024-01-03', sessions: 18, revenue: 2970, customers: 15, avgPrice: 165 }
	];

	await nextTick();
	initRevenueChart();
};

// 加载主题报表
const loadThemeReport = async (params: any) => {
	// 模拟数据
	themeStats.value = [
		{ themeName: '密室逃脱A', sessions: 45, revenue: 7560, customers: 32, avgRating: 4.5, utilizationRate: 85 },
		{ themeName: '恐怖屋B', sessions: 38, revenue: 4864, customers: 28, avgRating: 4.2, utilizationRate: 72 },
		{ themeName: '推理馆C', sessions: 42, revenue: 8316, customers: 35, avgRating: 4.8, utilizationRate: 90 }
	];

	await nextTick();
	initThemeChart();
};

// 加载会员报表
const loadMemberReport = async (params: any) => {
	// 模拟数据
	memberData.totalMembers = 156;
	memberData.newMembers = 23;
	memberData.totalRecharge = 45600;
	memberData.totalConsume = 38900;

	memberRanking.value = [
		{ memberName: '张三', phone: '13800138001', totalConsume: 2580, sessions: 15, lastVisit: new Date().toISOString() },
		{ memberName: '李四', phone: '13800138002', totalConsume: 2340, sessions: 12, lastVisit: new Date().toISOString() },
		{ memberName: '王五', phone: '13800138003', totalConsume: 2100, sessions: 10, lastVisit: new Date().toISOString() }
	];

	await nextTick();
	initMemberChart();
};

// 初始化营收图表
const initRevenueChart = () => {
	if (revenueChartInstance) {
		revenueChartInstance.dispose();
	}
	
	revenueChartInstance = echarts.init(revenueChart.value);
	const option = {
		title: {
			text: '营收趋势'
		},
		tooltip: {
			trigger: 'axis'
		},
		xAxis: {
			type: 'category',
			data: revenueDetails.value.map(item => item.date)
		},
		yAxis: {
			type: 'value'
		},
		series: [{
			data: revenueDetails.value.map(item => item.revenue),
			type: 'line',
			smooth: true
		}]
	};
	revenueChartInstance.setOption(option);
};

// 初始化主题图表
const initThemeChart = () => {
	if (themeChartInstance) {
		themeChartInstance.dispose();
	}
	
	themeChartInstance = echarts.init(themeChart.value);
	const option = {
		title: {
			text: '主题受欢迎程度'
		},
		tooltip: {
			trigger: 'item'
		},
		series: [{
			type: 'pie',
			radius: '50%',
			data: themeStats.value.map(item => ({
				value: item.sessions,
				name: item.themeName
			}))
		}]
	};
	themeChartInstance.setOption(option);
};

// 初始化会员图表
const initMemberChart = () => {
	if (memberChartInstance) {
		memberChartInstance.dispose();
	}
	
	memberChartInstance = echarts.init(memberChart.value);
	const option = {
		title: {
			text: '会员消费分布'
		},
		tooltip: {
			trigger: 'axis'
		},
		xAxis: {
			type: 'category',
			data: memberRanking.value.map(item => item.memberName)
		},
		yAxis: {
			type: 'value'
		},
		series: [{
			data: memberRanking.value.map(item => item.totalConsume),
			type: 'bar'
		}]
	};
	memberChartInstance.setOption(option);
};

// 导出报表
const exportReport = () => {
	ElMessage.info('导出功能开发中...');
};

// 格式化时间
const formatTime = (time: string) => {
	if (!time) return '-';
	return new Date(time).toLocaleString('zh-CN');
};

// 组件挂载时加载数据
onMounted(() => {
	loadReport();
});
</script>

<style lang="scss" scoped>
.game-report {
	padding: 20px;

	.filter-card {
		margin-bottom: 20px;
	}

	.stat-card {
		.stat-item {
			text-align: center;
			padding: 20px;

			.stat-value {
				font-size: 32px;
				font-weight: bold;
				color: #409eff;
				margin-bottom: 8px;
			}

			.stat-label {
				font-size: 14px;
				color: #666;
			}
		}
	}
}
</style>
