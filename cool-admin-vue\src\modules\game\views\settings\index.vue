<template>
	<div class="game-settings">
		<el-tabs v-model="activeTab" type="card">
			<!-- 营业时间设置 -->
			<el-tab-pane label="营业时间" name="businessHours">
				<el-card>
					<template #header>
						<div class="card-header">
							<span>营业时间设置</span>
							<el-button type="primary" @click="saveBusinessHours">保存设置</el-button>
						</div>
					</template>
					
					<div class="business-hours-grid">
						<div 
							v-for="(day, index) in weekDays" 
							:key="index"
							class="day-setting"
						>
							<div class="day-header">
								<el-checkbox 
									v-model="businessHours[index].isActive"
									:label="day"
								/>
							</div>
							<div class="time-setting" v-if="businessHours[index].isActive">
								<el-time-picker
									v-model="businessHours[index].startTime"
									placeholder="开始时间"
									format="HH:mm"
									value-format="HH:mm"
								/>
								<span class="time-separator">至</span>
								<el-time-picker
									v-model="businessHours[index].endTime"
									placeholder="结束时间"
									format="HH:mm"
									value-format="HH:mm"
								/>
							</div>
							<div v-else class="rest-day">休息日</div>
						</div>
					</div>
				</el-card>
			</el-tab-pane>

			<!-- 休息日管理 -->
			<el-tab-pane label="休息日管理" name="restDays">
				<el-row :gutter="20">
					<el-col :span="12">
						<el-card>
							<template #header>
								<span>固定休息日</span>
							</template>
							<el-checkbox-group v-model="fixedRestDays">
								<div v-for="(day, index) in weekDays" :key="index" class="rest-day-item">
									<el-checkbox :label="index">{{ day }}</el-checkbox>
								</div>
							</el-checkbox-group>
							<div class="mt-4">
								<el-button type="primary" @click="saveFixedRestDays">保存固定休息日</el-button>
							</div>
						</el-card>
					</el-col>
					
					<el-col :span="12">
						<el-card>
							<template #header>
								<div class="card-header">
									<span>临时休息日</span>
									<el-button type="primary" @click="showAddRestDayDialog">添加</el-button>
								</div>
							</template>
							<el-table :data="temporaryRestDays" style="width: 100%">
								<el-table-column prop="date" label="日期" width="120" />
								<el-table-column prop="reason" label="原因" />
								<el-table-column label="操作" width="80">
									<template #default="{ row }">
										<el-button 
											type="danger" 
											size="small"
											@click="removeRestDay(row.id)"
										>
											删除
										</el-button>
									</template>
								</el-table-column>
							</el-table>
						</el-card>
					</el-col>
				</el-row>
			</el-tab-pane>

			<!-- 场次参数配置 -->
			<el-tab-pane label="场次参数" name="sessionParams">
				<el-row :gutter="20">
					<el-col :span="12">
						<el-card>
							<template #header>
								<span>默认参数设置</span>
							</template>
							<el-form :model="defaultParams" label-width="140px">
								<el-form-item label="默认游戏时长">
									<el-input-number
										v-model="defaultParams.defaultGameDuration"
										:min="30"
										:max="300"
										:step="15"
									/>
									<span class="unit">分钟</span>
								</el-form-item>
								<el-form-item label="默认间隔时间">
									<el-input-number
										v-model="defaultParams.defaultIntervalTime"
										:min="5"
										:max="60"
										:step="5"
									/>
									<span class="unit">分钟</span>
								</el-form-item>
								<el-form-item label="最大提前预约">
									<el-input-number
										v-model="defaultParams.maxAdvanceBookingDays"
										:min="1"
										:max="90"
									/>
									<span class="unit">天</span>
								</el-form-item>
								<el-form-item>
									<el-button type="primary" @click="saveDefaultParams">保存默认参数</el-button>
								</el-form-item>
							</el-form>
						</el-card>
					</el-col>
					
					<el-col :span="12">
						<el-card>
							<template #header>
								<div class="card-header">
									<span>主题个性化设置</span>
									<el-button type="primary" @click="showThemeParamsDialog">设置主题参数</el-button>
								</div>
							</template>
							<el-table :data="themeParams" style="width: 100%">
								<el-table-column prop="themeName" label="主题名称" />
								<el-table-column prop="gameDuration" label="时长(分)" width="80" />
								<el-table-column prop="intervalTime" label="间隔(分)" width="80" />
								<el-table-column prop="maxPlayers" label="最大人数" width="80" />
								<el-table-column label="操作" width="80">
									<template #default="{ row }">
										<el-button 
											type="primary" 
											size="small"
											@click="editThemeParams(row)"
										>
											编辑
										</el-button>
									</template>
								</el-table-column>
							</el-table>
						</el-card>
					</el-col>
				</el-row>
			</el-tab-pane>
		</el-tabs>

		<!-- 添加临时休息日对话框 -->
		<el-dialog v-model="restDayDialogVisible" title="添加临时休息日" width="400px">
			<el-form :model="newRestDay" label-width="80px">
				<el-form-item label="日期" required>
					<el-date-picker
						v-model="newRestDay.date"
						type="date"
						placeholder="选择日期"
						style="width: 100%"
						:disabled-date="disabledDate"
					/>
				</el-form-item>
				<el-form-item label="原因" required>
					<el-input
						v-model="newRestDay.reason"
						placeholder="请输入休息原因"
					/>
				</el-form-item>
			</el-form>
			<template #footer>
				<el-button @click="restDayDialogVisible = false">取消</el-button>
				<el-button type="primary" @click="addRestDay">确定</el-button>
			</template>
		</el-dialog>

		<!-- 主题参数设置对话框 -->
		<el-dialog v-model="themeParamsDialogVisible" title="主题参数设置" width="500px">
			<el-form :model="currentThemeParams" label-width="100px">
				<el-form-item label="选择主题" required>
					<el-select v-model="currentThemeParams.themeId" placeholder="请选择主题" style="width: 100%">
						<el-option
							v-for="theme in themeOptions"
							:key="theme.id"
							:label="theme.name"
							:value="theme.id"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="游戏时长" required>
					<el-input-number
						v-model="currentThemeParams.gameDuration"
						:min="30"
						:max="300"
						:step="15"
					/>
					<span class="unit">分钟</span>
				</el-form-item>
				<el-form-item label="间隔时间" required>
					<el-input-number
						v-model="currentThemeParams.intervalTime"
						:min="5"
						:max="60"
						:step="5"
					/>
					<span class="unit">分钟</span>
				</el-form-item>
				<el-form-item label="最大人数" required>
					<el-input-number
						v-model="currentThemeParams.maxPlayers"
						:min="1"
						:max="20"
					/>
					<span class="unit">人</span>
				</el-form-item>
				<el-form-item label="单人价格" required>
					<el-input-number
						v-model="currentThemeParams.pricePerPlayer"
						:min="0"
						:precision="2"
					/>
					<span class="unit">元</span>
				</el-form-item>
			</el-form>
			<template #footer>
				<el-button @click="themeParamsDialogVisible = false">取消</el-button>
				<el-button type="primary" @click="saveThemeParams">确定</el-button>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useCool } from '/@/cool';
import { ElMessage, ElMessageBox } from 'element-plus';

defineOptions({
	name: 'game-settings'
});

const { service } = useCool();

// 当前激活的标签页
const activeTab = ref('businessHours');

// 星期数组
const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];

// 营业时间设置
const businessHours = ref(
	Array.from({ length: 7 }, (_, index) => ({
		dayOfWeek: index,
		startTime: '09:00',
		endTime: '22:00',
		isActive: index !== 0 // 默认周日休息
	}))
);

// 固定休息日
const fixedRestDays = ref<number[]>([0]); // 默认周日休息

// 临时休息日
const temporaryRestDays = ref([]);

// 默认参数
const defaultParams = ref({
	defaultGameDuration: 90,
	defaultIntervalTime: 15,
	maxAdvanceBookingDays: 30
});

// 主题参数
const themeParams = ref([]);
const themeOptions = ref([]);

// 对话框状态
const restDayDialogVisible = ref(false);
const themeParamsDialogVisible = ref(false);

// 新增休息日表单
const newRestDay = ref({
	date: '',
	reason: ''
});

// 当前编辑的主题参数
const currentThemeParams = ref({
	themeId: null,
	gameDuration: 90,
	intervalTime: 15,
	maxPlayers: 6,
	pricePerPlayer: 0
});

// 加载营业时间
const loadBusinessHours = async () => {
	try {
		const res = await service.game.sessionSetting.getBusinessHours();
		if (res && res.length > 0) {
			businessHours.value = res;
		}
	} catch (error) {
		console.error('加载营业时间失败:', error);
	}
};

// 保存营业时间
const saveBusinessHours = async () => {
	try {
		for (const hours of businessHours.value) {
			await service.game.sessionSetting.setBusinessHours(hours);
		}
		ElMessage.success('营业时间保存成功');
	} catch (error) {
		ElMessage.error('保存营业时间失败');
	}
};

// 加载休息日设置
const loadRestDays = async () => {
	try {
		const res = await service.game.sessionSetting.getRestDays();
		if (res) {
			fixedRestDays.value = res.fixedRestDays || [];
			temporaryRestDays.value = res.temporaryRestDays || [];
		}
	} catch (error) {
		console.error('加载休息日设置失败:', error);
	}
};

// 保存固定休息日
const saveFixedRestDays = async () => {
	try {
		await service.game.sessionSetting.setFixedRestDays({ dayOfWeek: fixedRestDays.value });
		ElMessage.success('固定休息日保存成功');
	} catch (error) {
		ElMessage.error('保存固定休息日失败');
	}
};

// 显示添加休息日对话框
const showAddRestDayDialog = () => {
	newRestDay.value = { date: '', reason: '' };
	restDayDialogVisible.value = true;
};

// 添加临时休息日
const addRestDay = async () => {
	if (!newRestDay.value.date || !newRestDay.value.reason) {
		ElMessage.warning('请填写完整信息');
		return;
	}

	try {
		await service.game.sessionSetting.addTemporaryRestDay({
			...newRestDay.value,
			isActive: true
		});
		ElMessage.success('添加成功');
		restDayDialogVisible.value = false;
		loadRestDays();
	} catch (error) {
		ElMessage.error('添加失败');
	}
};

// 删除临时休息日
const removeRestDay = async (id: number) => {
	try {
		await ElMessageBox.confirm('确定要删除这个休息日吗？', '确认删除', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		});

		await service.game.sessionSetting.removeTemporaryRestDay({ id });
		ElMessage.success('删除成功');
		loadRestDays();
	} catch (error) {
		if (error !== 'cancel') {
			ElMessage.error('删除失败');
		}
	}
};

// 禁用过去的日期
const disabledDate = (time: Date) => {
	return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
};

// 加载场次参数
const loadSessionParams = async () => {
	try {
		const res = await service.game.sessionSetting.getSessionParams();
		if (res) {
			defaultParams.value = res;
		}
	} catch (error) {
		console.error('加载场次参数失败:', error);
	}
};

// 保存默认参数
const saveDefaultParams = async () => {
	try {
		await service.game.sessionSetting.setDefaultSessionParams(defaultParams.value);
		ElMessage.success('默认参数保存成功');
	} catch (error) {
		ElMessage.error('保存默认参数失败');
	}
};

// 加载主题选项
const loadThemeOptions = async () => {
	try {
		const res = await service.game.theme.list();
		themeOptions.value = res || [];
	} catch (error) {
		console.error('加载主题选项失败:', error);
	}
};

// 加载主题参数
const loadThemeParams = async () => {
	try {
		const themes = await service.game.theme.list();
		const params = [];

		for (const theme of themes || []) {
			try {
				const themeParam = await service.game.sessionSetting.getThemeSessionParams(theme.id);
				if (themeParam) {
					params.push({
						...themeParam,
						themeName: theme.name
					});
				}
			} catch (error) {
				// 如果主题没有设置参数，跳过
			}
		}

		themeParams.value = params;
	} catch (error) {
		console.error('加载主题参数失败:', error);
	}
};

// 显示主题参数对话框
const showThemeParamsDialog = () => {
	currentThemeParams.value = {
		themeId: null,
		gameDuration: defaultParams.value.defaultGameDuration,
		intervalTime: defaultParams.value.defaultIntervalTime,
		maxPlayers: 6,
		pricePerPlayer: 0
	};
	themeParamsDialogVisible.value = true;
};

// 编辑主题参数
const editThemeParams = (row: any) => {
	currentThemeParams.value = { ...row };
	themeParamsDialogVisible.value = true;
};

// 保存主题参数
const saveThemeParams = async () => {
	if (!currentThemeParams.value.themeId) {
		ElMessage.warning('请选择主题');
		return;
	}

	try {
		await service.game.sessionSetting.setThemeSessionParams(currentThemeParams.value);
		ElMessage.success('主题参数保存成功');
		themeParamsDialogVisible.value = false;
		loadThemeParams();
	} catch (error) {
		ElMessage.error('保存主题参数失败');
	}
};

// 初始化
onMounted(() => {
	loadBusinessHours();
	loadRestDays();
	loadSessionParams();
	loadThemeOptions();
	loadThemeParams();
});
</script>

<style lang="scss" scoped>
.game-settings {
	padding: 20px;
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.business-hours-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
	gap: 20px;
}

.day-setting {
	border: 1px solid #e4e7ed;
	border-radius: 8px;
	padding: 16px;
	background: #fafafa;
}

.day-header {
	margin-bottom: 12px;
	font-weight: bold;
}

.time-setting {
	display: flex;
	align-items: center;
	gap: 10px;
}

.time-separator {
	color: #909399;
	font-size: 14px;
}

.rest-day {
	color: #f56c6c;
	font-size: 14px;
	text-align: center;
	padding: 8px;
}

.rest-day-item {
	margin-bottom: 12px;
}

.unit {
	margin-left: 8px;
	color: #909399;
	font-size: 14px;
}

.mt-4 {
	margin-top: 16px;
}

:deep(.el-tabs__content) {
	padding-top: 20px;
}

:deep(.el-card) {
	margin-bottom: 20px;
}

:deep(.el-form-item) {
	margin-bottom: 18px;
}

:deep(.el-input-number) {
	width: 120px;
}

:deep(.el-time-picker) {
	width: 120px;
}
</style>
</script>
