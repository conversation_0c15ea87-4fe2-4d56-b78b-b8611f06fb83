2025-08-01 20:09:56.910 INFO 14228 [36m [cool:core] midwayjs cool core func handler [0m
2025-08-01 20:09:57.010 INFO 14228 [36m [cool:module:swagger] midwayjs cool module swagger build success[0m
2025-08-01 20:09:59.147 INFO 14228 [36m [cool:core] midwayjs cool core init base database complete [0m
2025-08-01 20:09:59.253 INFO 14228 [36m [cool:core] midwayjs cool core init dict database complete [0m
2025-08-01 20:09:59.322 INFO 14228 [36m [cool:core] midwayjs cool core init game database complete [0m
2025-08-01 20:09:59.359 INFO 14228 [36m [cool:core] midwayjs cool core init task database complete [0m
2025-08-01 20:10:00.347 INFO 14228 [36m [cool:module:base] midwayjs cool module base import [base] module menu success [0m
2025-08-01 20:10:00.358 INFO 14228 [36m [cool:module:base] midwayjs cool module base import [game] module menu success [0m
2025-08-01 20:10:39.948 INFO 7996 [36m [cool:core] midwayjs cool core func handler [0m
2025-08-01 20:10:40.052 INFO 7996 [36m [cool:module:swagger] midwayjs cool module swagger build success[0m
2025-08-01 20:13:11.602 INFO 13492 [36m [cool:core] midwayjs cool core func handler [0m
2025-08-01 20:13:17.183 INFO 7956 [36m [cool:core] midwayjs cool core func handler [0m
2025-08-01 20:13:22.912 INFO 1728 [36m [cool:core] midwayjs cool core func handler [0m
2025-08-01 20:13:28.534 INFO 15760 [36m [cool:core] midwayjs cool core func handler [0m
2025-08-01 20:13:34.152 INFO 11568 [36m [cool:core] midwayjs cool core func handler [0m
2025-08-01 20:14:19.027 INFO 8028 [36m [cool:core] midwayjs cool core func handler [0m
2025-08-01 20:14:19.134 INFO 8028 [36m [cool:module:swagger] midwayjs cool module swagger build success[0m
2025-08-01 20:14:49.176 INFO 8028 [midway:static] starting static serve / -> C:\dev\yh\cool-admin-midway\public
2025-08-01 20:14:49.178 INFO 8028 [midway:static] starting static serve /upload -> C:\Users\<USER>\.cool-admin\756243a97270d787eee4d7b2945b379f\upload
2025-08-01 20:26:04.730 INFO 16060 [36m [cool:core] midwayjs cool core func handler [0m
2025-08-01 20:26:04.836 INFO 16060 [36m [cool:module:swagger] midwayjs cool module swagger build success[0m
2025-08-01 20:26:36.751 INFO 15540 [36m [cool:core] midwayjs cool core func handler [0m
2025-08-01 20:26:36.847 INFO 15540 [36m [cool:module:swagger] midwayjs cool module swagger build success[0m
2025-08-01 20:27:48.784 INFO 15540 [midway:static] starting static serve / -> C:\dev\yh\cool-admin-midway\public
2025-08-01 20:27:48.789 INFO 15540 [midway:static] starting static serve /upload -> C:\Users\<USER>\.cool-admin\756243a97270d787eee4d7b2945b379f\upload
2025-08-01 20:52:00.240 INFO 9312 [36m [cool:core] midwayjs cool core func handler [0m
2025-08-01 20:52:00.349 INFO 9312 [36m [cool:module:swagger] midwayjs cool module swagger build success[0m
2025-08-01 20:52:08.055 INFO 9312 [midway:static] starting static serve / -> C:\dev\yh\cool-admin-midway\public
2025-08-01 20:52:08.059 INFO 9312 [midway:static] starting static serve /upload -> C:\Users\<USER>\.cool-admin\756243a97270d787eee4d7b2945b379f\upload
