<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn />
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />
			<cl-flex1 />
			<!-- 关键字搜索 -->
			<cl-search-key />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table">
				<!-- 主题名称 -->
				<template #column-themeName="{ scope }">
					<span style="font-weight: bold;">{{ scope.row.themeName || '-' }}</span>
				</template>

				<!-- 状态 -->
				<template #column-status="{ scope }">
					<el-tag :type="getStatusType(scope.row.status)">
						{{ getStatusText(scope.row.status) }}
					</el-tag>
				</template>

				<!-- 价格 -->
				<template #column-totalPrice="{ scope }">
					<span style="color: #f56c6c; font-weight: bold;">¥{{ scope.row.totalPrice }}</span>
				</template>

				<!-- 时间范围 -->
				<template #column-timeRange="{ scope }">
					<div>
						<div>开始: {{ formatTime(scope.row.startTime) }}</div>
						<div>结束: {{ formatTime(scope.row.endTime) }}</div>
					</div>
				</template>

				<!-- 操作按钮 -->
				<template #slot-btns1="{ scope }">
						<el-button 
							v-if="scope.row.status === 0"
							type="success"
							@click="startGame(scope.row)"
						>
							开始游戏
						</el-button>
						<el-button 
							v-if="scope.row.status === 1"
							type="warning"
							@click="endGame(scope.row)"
						>
							结束游戏
						</el-button>
				</template>
				<!-- 操作按钮 -->
				<template #slot-btns2="{ scope }">
						<el-button 
							v-if="[0, 1].includes(scope.row.status)"
							type="danger"
							@click="cancelGame(scope.row)"
						>
							取消游戏
						</el-button>
				</template>
			</cl-table>
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert">
			<template #slot-theme="{ scope }">
				<el-select
					v-model="scope.themeId"
					placeholder="请选择主题"
					style="width: 100%"
					@change="onThemeChange"
				>
					<el-option
						v-for="theme in themeOptions"
						:key="theme.id"
						:label="theme.name"
						:value="theme.id"
					/>
				</el-select>
			</template>
			<template #slot-startTime="{ scope }">
				<el-date-picker
					v-model="scope.startTime"
					type="datetime"
					:default-value="defaultDateTime"
					:shortcuts="timeShortcuts"
					@change="onStartTimeChange"
					placeholder="选择开始时间"
					style="width: 100%"
				/>
			</template>
			<template #slot-endTime="{ scope }">
				<el-date-picker
					v-model="scope.endTime"
					type="datetime"
					:default-value="defaultDateTime"
					:shortcuts="timeShortcuts"
					placeholder="选择开始时间"
					style="width: 100%"
				/>
			</template>
		</cl-upsert>
	</cl-crud>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useCrud, useTable, useUpsert } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { ElMessage, ElMessageBox } from 'element-plus';

defineOptions({
	name: 'game-session'
});

const { service } = useCool();

// 主题选项
const themeOptions = ref([]);

// cl-crud 配置
const Crud = useCrud(
	{
		service: service.game.session
	},
	(app) => {
		app.refresh();
		loadThemeOptions();
	}
);

// cl-table 配置
const Table = useTable({
	columns: [
		{
			type: 'selection',
			width: 60
		},
		{
			label: '主题名称',
			prop: 'themeName',
			minWidth: 150
		},
		{
			label: '客户姓名',
			prop: 'playerName',
			width: 120
		},
		{
			label: '客户电话',
			prop: 'phone',
			width: 130
		},
		{
			label: '游戏人数',
			prop: 'playerCount',
			width: 100
		},
		{
			label: '总价格',
			prop: 'totalPrice',
			width: 100
		},
		{
			label: '时间安排',
			prop: 'timeRange',
			width: 200
		},
		{
			label: '状态',
			prop: 'status',
			width: 100
		},
		{
			label: '创建时间',
			prop: 'createTime',
			width: 160,
			sortable: 'desc'
		},
		{
			type: 'op',
			buttons: [
				'edit',
			// 'slot-btns1',
				'slot-btns2'
			],
			width: 200
		}
	]
});

// 当前选中主题
const currTheme = ref<any>({});

// 获取当前日期+2分钟作为默认时间
const defaultDateTime = computed(() => {
  const now = new Date();
  now.setMinutes(now.getMinutes() + 2);
  return now;
});

// 禁用今天以外的所有日期
const disableOtherDates = (date: Date) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return date < today || date > today;
};

// 时间选择快捷选项
const timeShortcuts = ref([
  {
    text: '2分钟后',
    value: () => {
      const time = new Date();
      time.setMinutes(time.getMinutes() + 2);
      return time;
    }
  },
  {
    text: '5分钟后',
    value: () => {
      const time = new Date();
      time.setMinutes(time.getMinutes() + 5);
      return time;
    }
  },
  {
    text: '15分钟后',
    value: () => {
      const time = new Date();
      time.setMinutes(time.getMinutes() + 15);
      return time;
    }
  }
]);

// cl-upsert 配置
const Upsert = useUpsert({
	items: [
		{
			label: '选择主题',
			prop: 'themeId',
			required: true,
			component: {
				name: 'slot-theme'
			}
		},
		{
			label: '客户姓名',
			prop: 'playerName',
			required: false,
			component: {
				name: 'el-input',
				props: {
					placeholder: '请输入客户姓名'
				}
			}
		},
		{
			label: '客户电话',
			prop: 'phone',
			required: false,
			component: {
				name: 'el-input',
				props: {
					placeholder: '请输入客户电话'
				}
			}
		},
		{
			label: '游戏人数',
			prop: 'playerCount',
			required: true,
			component: {
				name: 'el-input-number',
				props: {
					min: 1,
					max: 20,
					placeholder: '游戏人数'
				}
			}
		},
		{
			label: '开始时间',
			prop: 'startTime',
			required: true,
			component: {
				name: 'slot-startTime'
			}
		},
		{
			label: '结束时间',
			prop: 'endTime',
			required: true,
			component: {
				name: 'slot-endTime'
			}
		},
		{
			label: '备注',
			prop: 'remark',
			component: {
				name: 'el-input',
				props: {
					type: 'textarea',
					rows: 3,
					placeholder: '请输入备注信息'
				}
			}
		}
	]
});

// 加载主题选项
const loadThemeOptions = async () => {
	try {
		const res = await service.game.theme.list();
		console.log("theme.list:",res)
		themeOptions.value = res || [];

		// 如果加载了主题选项
		if (themeOptions.value.length > 0) {
			// 设置当前主题为第一项
			currTheme.value = themeOptions.value[0];
			
			// 如果Upsert实例已创建，设置默认主题
			if (Upsert.value) {
				Upsert.value.setForm('themeId', themeOptions.value[0].id);
			}
		}

	} catch (error) {
		console.error('加载主题选项失败:', error);
	}
};

// 主题变化处理
const onThemeChange = (themeId: number) => {
	const theme = themeOptions.value.find((t: any) => t.id === themeId);
	if (theme) {
		// 可以在这里设置默认价格等信息
		console.log('选择的主题:', theme);
		// Upsert.value?.setForm("currTheme", theme);
		currTheme.value = theme;

		// 如果开始时间有值，更新结束时间
		const startTime = Upsert.value?.form.startTime;
		if (startTime) {
			updateEndTime(startTime);
		}

	}
};

const onStartTimeChange = (value: any) => {
	console.log('onStartTimeChange:', value);
	// 如果有选中主题，更新结束时间
	if (currTheme.value?.id && value) {
		updateEndTime(value);
	}
};

// 更新结束时间 (开始时间 + 主题时长)
const updateEndTime = (startTime: Date) => {
	console.log('updateEndTime:', startTime);
	const endTime = new Date(startTime);
	endTime.setMinutes(endTime.getMinutes() + currTheme.value.duration);
	Upsert.value?.setForm("endTime", endTime);
};

// 格式化时间
const formatTime = (time: string) => {
	if (!time) return '-';
	return new Date(time).toLocaleString('zh-CN');
};

// 获取状态类型
const getStatusType = (status: number) => {
	const typeMap = {
		0: 'info',       // 待开始
		1: 'warning',    // 进行中
		2: 'success',    // 已完成
		3: 'danger'      // 已取消
	};
	return typeMap[status] || 'info';
};

// 获取状态文本
const getStatusText = (status: number) => {
	const textMap = {
		0: '待开始',
		1: '进行中',
		2: '已完成',
		3: '已取消'
	};
	return textMap[status] || '未知';
};

// 编辑场次
const editSession = (row: any) => {
	console.log("editSession:",row);
	Upsert.value?.open({
		type: 'update',
		data: row
	});
};

// 开始游戏
const startGame = async (row: any) => {
	try {
		await ElMessageBox.confirm(
			`确定要开始游戏"${row.themeName}"吗？`,
			'开始游戏',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}
		);

		await service.game.session.startGame({
			// id: row.id,
			themeId: row.themeId,
			// playerName: row.playerName,
			playerCount: row.playerCount,
			startTime: row.startTime,
			// endTime: row.endTime,
			paymentType: 1
		});
		ElMessage.success('游戏已开始');
		Crud.value?.refresh();
	} catch (error) {
		if (error !== 'cancel') {
			ElMessage.error('开始游戏失败');
		}
	}
};

// 结束游戏
const endGame = async (row: any) => {
	try {
		await ElMessageBox.confirm(
			`确定要结束游戏"${row.theme?.name}"吗？`,
			'结束游戏',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}
		);

		await service.game.session.endGame({ id: row.id });
		ElMessage.success('游戏已结束');
		Crud.value?.refresh();
	} catch (error) {
		if (error !== 'cancel') {
			ElMessage.error('结束游戏失败');
		}
	}
};

// 取消游戏
const cancelGame = async (row: any) => {
	try {
		await ElMessageBox.confirm(
			`确定要取消游戏"${row.theme?.name}"吗？此操作不可恢复！`,
			'取消游戏',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}
		);

		await service.game.session.cancelGame({ id: row.id });
		ElMessage.success('游戏已取消');
		Crud.value?.refresh();
	} catch (error) {
		if (error !== 'cancel') {
			ElMessage.error('取消游戏失败');
		}
	}
};
</script>

<style lang="scss" scoped>
.el-button-group {
	.el-button {
		margin-left: 0;
	}
}
</style>
