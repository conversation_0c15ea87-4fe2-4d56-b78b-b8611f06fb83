<template>
	<div class="smart-time-picker">
		<div class="picker-header">
			<el-radio-group v-model="selectionMode" @change="onModeChange">
				<el-radio-button label="grid">场次网格</el-radio-button>
				<el-radio-button label="manual">手动选择</el-radio-button>
			</el-radio-group>
		</div>

		<!-- 场次网格模式 -->
		<div v-if="selectionMode === 'grid'" class="grid-mode">
			<SessionGrid
				ref="sessionGridRef"
				:theme-options="themeOptions"
				v-model="selectedTime"
				@slot-selected="onSlotSelected"
			/>
		</div>

		<!-- 手动选择模式 -->
		<div v-else class="manual-mode">
			<el-row :gutter="20">
				<el-col :span="12">
					<el-form-item label="选择主题" required>
						<el-select
							v-model="selectedThemeId"
							placeholder="请选择主题"
							style="width: 100%"
							@change="onThemeChange"
						>
							<el-option
								v-for="theme in themeOptions"
								:key="theme.id"
								:label="`${theme.name} (${theme.duration || 90}分钟)`"
								:value="theme.id"
							/>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="游戏时长" required>
						<el-input-number
							v-model="gameDuration"
							:min="30"
							:max="300"
							:step="15"
							@change="updateEndTime"
						/>
						<span class="unit">分钟</span>
					</el-form-item>
				</el-col>
			</el-row>

			<el-row :gutter="20">
				<el-col :span="12">
					<el-form-item label="开始时间" required>
						<el-date-picker
							v-model="startTime"
							type="datetime"
							placeholder="选择开始时间"
							style="width: 100%"
							:disabled-date="disabledDate"
							:disabled-hours="disabledHours"
							:disabled-minutes="disabledMinutes"
							@change="onStartTimeChange"
						/>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="结束时间" required>
						<el-date-picker
							v-model="endTime"
							type="datetime"
							placeholder="结束时间"
							style="width: 100%"
							:disabled="true"
						/>
					</el-form-item>
				</el-col>
			</el-row>

			<!-- 冲突检测结果 -->
			<div v-if="conflictInfo" class="conflict-info">
				<el-alert
					:title="conflictInfo.title"
					:type="conflictInfo.type"
					:description="conflictInfo.description"
					show-icon
					:closable="false"
				/>
				
				<!-- 推荐时段 -->
				<div v-if="recommendedSlots.length > 0" class="recommended-slots">
					<h4>推荐可用时段：</h4>
					<div class="slot-list">
						<el-button
							v-for="slot in recommendedSlots"
							:key="slot.startTime"
							type="primary"
							plain
							size="small"
							@click="selectRecommendedSlot(slot)"
						>
							{{ formatTime(slot.startTime) }} - {{ formatTime(slot.endTime) }}
						</el-button>
					</div>
				</div>
			</div>

			<!-- 可用时段显示 -->
			<div v-if="availableSlots.length > 0" class="available-slots">
				<h4>今日可用时段：</h4>
				<div class="slot-list">
					<el-tag
						v-for="slot in availableSlots"
						:key="slot.startTime"
						type="success"
						class="slot-tag"
						@click="selectAvailableSlot(slot)"
					>
						{{ formatTime(slot.startTime) }} - {{ formatTime(slot.endTime) }}
					</el-tag>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useCool } from '/@/cool';
import { ElMessage } from 'element-plus';
import SessionGrid from './SessionGrid.vue';

interface TimeSlot {
	startTime: string;
	endTime: string;
}

interface ConflictInfo {
	title: string;
	type: 'success' | 'warning' | 'error' | 'info';
	description: string;
}

interface Props {
	themeOptions: any[];
	modelValue?: {
		startTime: string;
		endTime: string;
	};
}

interface Emits {
	(e: 'update:modelValue', value: { startTime: string; endTime: string }): void;
	(e: 'theme-change', themeId: number): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { service } = useCool();

// 响应式数据
const selectionMode = ref<'grid' | 'manual'>('grid');
const selectedThemeId = ref<number>();
const gameDuration = ref(90);
const startTime = ref<Date>();
const endTime = ref<Date>();
const selectedTime = ref<TimeSlot>();

const conflictInfo = ref<ConflictInfo | null>(null);
const recommendedSlots = ref<TimeSlot[]>([]);
const availableSlots = ref<TimeSlot[]>([]);
const businessHours = ref<any[]>([]);

const sessionGridRef = ref();

// 计算属性
const formattedStartTime = computed(() => {
	return startTime.value ? startTime.value.toISOString() : '';
});

const formattedEndTime = computed(() => {
	return endTime.value ? endTime.value.toISOString() : '';
});

// 方法
const onModeChange = () => {
	conflictInfo.value = null;
	recommendedSlots.value = [];
	if (selectionMode.value === 'manual') {
		loadAvailableSlots();
	}
};

const onThemeChange = () => {
	if (selectedThemeId.value) {
		emit('theme-change', selectedThemeId.value);
		loadThemeParams();
		if (selectionMode.value === 'manual') {
			loadAvailableSlots();
		}
	}
};

const loadThemeParams = async () => {
	if (!selectedThemeId.value) return;
	
	try {
		const params = await service.game.sessionSetting.getThemeSessionParams(selectedThemeId.value);
		if (params) {
			gameDuration.value = params.gameDuration || 90;
		}
	} catch (error) {
		// 使用默认值
	}
};

const loadBusinessHours = async () => {
	try {
		const res = await service.game.sessionSetting.getBusinessHours();
		businessHours.value = res || [];
	} catch (error) {
		console.error('加载营业时间失败:', error);
	}
};

const loadAvailableSlots = async () => {
	if (!selectedThemeId.value) return;
	
	try {
		const today = new Date().toISOString().split('T')[0];
		const res = await service.game.session.getAvailableTimeSlots({
			themeId: selectedThemeId.value,
			date: today,
			duration: gameDuration.value
		});
		availableSlots.value = res?.slots || [];
	} catch (error) {
		console.error('加载可用时段失败:', error);
	}
};

const disabledDate = (time: Date) => {
	return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
};

const disabledHours = () => {
	if (!startTime.value) return [];
	
	const date = startTime.value;
	const dayOfWeek = date.getDay();
	const todayHours = businessHours.value.find(h => h.dayOfWeek === dayOfWeek);
	
	if (!todayHours || !todayHours.isActive) {
		return Array.from({ length: 24 }, (_, i) => i); // 全部禁用
	}
	
	const startHour = parseInt(todayHours.startTime.split(':')[0]);
	const endHour = parseInt(todayHours.endTime.split(':')[0]);
	
	const disabled = [];
	for (let i = 0; i < 24; i++) {
		if (i < startHour || i > endHour) {
			disabled.push(i);
		}
	}
	return disabled;
};

const disabledMinutes = (hour: number) => {
	if (!startTime.value) return [];
	
	const date = startTime.value;
	const dayOfWeek = date.getDay();
	const todayHours = businessHours.value.find(h => h.dayOfWeek === dayOfWeek);
	
	if (!todayHours || !todayHours.isActive) return [];
	
	const startHour = parseInt(todayHours.startTime.split(':')[0]);
	const startMinute = parseInt(todayHours.startTime.split(':')[1]);
	const endHour = parseInt(todayHours.endTime.split(':')[0]);
	const endMinute = parseInt(todayHours.endTime.split(':')[1]);
	
	const disabled = [];
	
	if (hour === startHour) {
		for (let i = 0; i < startMinute; i++) {
			disabled.push(i);
		}
	}
	
	if (hour === endHour) {
		for (let i = endMinute + 1; i < 60; i++) {
			disabled.push(i);
		}
	}
	
	return disabled;
};

const onStartTimeChange = () => {
	updateEndTime();
	checkConflict();
};

const updateEndTime = () => {
	if (startTime.value && gameDuration.value) {
		const end = new Date(startTime.value.getTime() + gameDuration.value * 60 * 1000);
		endTime.value = end;
		
		// 更新模型值
		emit('update:modelValue', {
			startTime: formattedStartTime.value,
			endTime: end.toISOString()
		});
	}
};

const checkConflict = async () => {
	if (!selectedThemeId.value || !startTime.value || !endTime.value) {
		conflictInfo.value = null;
		return;
	}
	
	try {
		const res = await service.game.session.checkTimeConflict({
			themeId: selectedThemeId.value,
			startTime: formattedStartTime.value,
			endTime: formattedEndTime.value
		});
		
		if (res.hasConflict) {
			conflictInfo.value = {
				title: '时间冲突',
				type: 'error',
				description: res.message || '选择的时间段与已有预约冲突'
			};
			
			// 获取推荐时段
			if (res.recommendedSlots) {
				recommendedSlots.value = res.recommendedSlots;
			}
		} else {
			conflictInfo.value = {
				title: '时间可用',
				type: 'success',
				description: '选择的时间段可以预约'
			};
			recommendedSlots.value = [];
		}
	} catch (error) {
		console.error('检查时间冲突失败:', error);
	}
};

const selectRecommendedSlot = (slot: TimeSlot) => {
	startTime.value = new Date(slot.startTime);
	endTime.value = new Date(slot.endTime);
	emit('update:modelValue', slot);
	checkConflict();
};

const selectAvailableSlot = (slot: TimeSlot) => {
	selectRecommendedSlot(slot);
};

const onSlotSelected = (slot: any) => {
	selectedTime.value = {
		startTime: slot.startTime,
		endTime: slot.endTime
	};
	emit('update:modelValue', selectedTime.value);
};

const formatTime = (time: string) => {
	return new Date(time).toLocaleString('zh-CN', {
		month: '2-digit',
		day: '2-digit',
		hour: '2-digit',
		minute: '2-digit'
	});
};

// 监听器
watch(() => props.themeOptions, (newOptions) => {
	if (newOptions.length > 0 && !selectedThemeId.value) {
		selectedThemeId.value = newOptions[0].id;
		onThemeChange();
	}
}, { immediate: true });

watch(() => props.modelValue, (newValue) => {
	if (newValue) {
		startTime.value = new Date(newValue.startTime);
		endTime.value = new Date(newValue.endTime);
	}
}, { immediate: true });

// 初始化
onMounted(() => {
	loadBusinessHours();
});

// 暴露方法
defineExpose({
	checkConflict,
	loadAvailableSlots
});
</script>

<style lang="scss" scoped>
.smart-time-picker {
	.picker-header {
		margin-bottom: 20px;
		padding: 16px;
		background: #f8f9fa;
		border-radius: 8px;
		text-align: center;
	}

	.manual-mode {
		.unit {
			margin-left: 8px;
			color: #909399;
			font-size: 14px;
		}

		.conflict-info {
			margin-top: 20px;

			.recommended-slots {
				margin-top: 16px;

				h4 {
					margin-bottom: 12px;
					color: #303133;
					font-size: 14px;
				}

				.slot-list {
					display: flex;
					flex-wrap: wrap;
					gap: 8px;
				}
			}
		}

		.available-slots {
			margin-top: 20px;
			padding: 16px;
			background: #f0f9ff;
			border-radius: 8px;

			h4 {
				margin-bottom: 12px;
				color: #303133;
				font-size: 14px;
			}

			.slot-list {
				display: flex;
				flex-wrap: wrap;
				gap: 8px;

				.slot-tag {
					cursor: pointer;
					transition: all 0.3s ease;

					&:hover {
						transform: translateY(-2px);
						box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
					}
				}
			}
		}
	}
}

:deep(.el-form-item) {
	margin-bottom: 18px;
}

:deep(.el-alert) {
	margin-bottom: 16px;
}

:deep(.el-radio-group) {
	.el-radio-button__inner {
		padding: 8px 20px;
	}
}
</style>
