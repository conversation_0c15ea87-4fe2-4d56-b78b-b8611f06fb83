import { BaseService } from '/@/cool';

// 游戏报表服务
export default class GameReportService extends BaseService {
	namespace = '/admin/game/report';

	// 获取营收报表
	getRevenueReport(params: { startDate: string; endDate: string }) {
		return this.request({
			url: '/revenue',
			method: 'get',
			params
		});
	}

	// 获取主题统计
	getThemeStats(params: { startDate: string; endDate: string }) {
		return this.request({
			url: '/themeStats',
			method: 'get',
			params
		});
	}

	// 获取会员统计
	getMemberStats(params: { startDate: string; endDate: string }) {
		return this.request({
			url: '/memberStats',
			method: 'get',
			params
		});
	}
}