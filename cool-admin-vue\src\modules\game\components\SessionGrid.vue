<template>
	<div class="session-grid">
		<div class="grid-header">
			<div class="date-selector">
				<el-date-picker
					v-model="selectedDate"
					type="date"
					placeholder="选择日期"
					@change="onDateChange"
					:disabled-date="disabledDate"
				/>
			</div>
			<div class="theme-selector">
				<el-select
					v-model="selectedThemeId"
					placeholder="选择主题"
					@change="onThemeChange"
					style="width: 200px"
				>
					<el-option
						v-for="theme in themeOptions"
						:key="theme.id"
						:label="theme.name"
						:value="theme.id"
					/>
				</el-select>
			</div>
			<div class="actions">
				<el-button @click="generatePlan" type="primary" :loading="generating">
					生成场次计划
				</el-button>
				<el-button @click="refreshPlan" :loading="loading">
					刷新
				</el-button>
			</div>
		</div>

		<div class="grid-legend">
			<div class="legend-item">
				<div class="legend-color available"></div>
				<span>可预约</span>
			</div>
			<div class="legend-item">
				<div class="legend-color reserved"></div>
				<span>已预约</span>
			</div>
			<div class="legend-item">
				<div class="legend-color ongoing"></div>
				<span>进行中</span>
			</div>
			<div class="legend-item">
				<div class="legend-color maintenance"></div>
				<span>维护中</span>
			</div>
			<div class="legend-item">
				<div class="legend-color closed"></div>
				<span>已关闭</span>
			</div>
		</div>

		<div class="session-slots" v-loading="loading">
			<div
				v-for="slot in sessionSlots"
				:key="slot.id"
				:class="getSlotClass(slot)"
				@click="onSlotClick(slot)"
				@mouseenter="showTooltip($event, slot)"
				@mouseleave="hideTooltip"
			>
				<div class="slot-time">
					{{ formatTime(slot.startTime) }} - {{ formatTime(slot.endTime) }}
				</div>
				<div class="slot-info">
					<div class="slot-status">{{ getStatusText(slot.status) }}</div>
					<div v-if="slot.currentPlayers" class="slot-players">
						{{ slot.currentPlayers }}/{{ slot.maxPlayers }}人
					</div>
				</div>
			</div>
		</div>

		<!-- 空状态 -->
		<el-empty v-if="!loading && sessionSlots.length === 0" description="暂无场次计划">
			<el-button type="primary" @click="generatePlan">生成场次计划</el-button>
		</el-empty>

		<!-- 悬浮提示 -->
		<div
			ref="tooltipRef"
			v-show="tooltipVisible"
			class="session-tooltip"
			:style="tooltipStyle"
		>
			<div v-if="tooltipData">
				<div class="tooltip-title">{{ formatTime(tooltipData.startTime) }} - {{ formatTime(tooltipData.endTime) }}</div>
				<div class="tooltip-content">
					<div><strong>状态:</strong> {{ getStatusText(tooltipData.status) }}</div>
					<div v-if="tooltipData.pricePerPlayer">
						<strong>价格:</strong> ¥{{ tooltipData.pricePerPlayer }}/人
					</div>
					<div v-if="tooltipData.maxPlayers">
						<strong>最大人数:</strong> {{ tooltipData.maxPlayers }}人
					</div>
					<div v-if="tooltipData.currentPlayers !== undefined">
						<strong>当前预约:</strong> {{ tooltipData.currentPlayers }}人
					</div>
					<div v-if="tooltipData.customerName">
						<strong>预约人:</strong> {{ tooltipData.customerName }}
					</div>
					<div v-if="tooltipData.status === 0" class="tooltip-hint">
						点击选择此时段
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, nextTick } from 'vue';
import { useCool } from '/@/cool';
import { ElMessage } from 'element-plus';

interface SessionSlot {
	id?: number;
	startTime: string;
	endTime: string;
	status: number; // 0: 可用, 1: 已预约, 2: 进行中, 3: 已关闭, 4: 维护中
	maxPlayers?: number;
	currentPlayers?: number;
	pricePerPlayer?: number;
	customerName?: string;
	themeId: number;
}

interface Props {
	themeOptions: any[];
	modelValue?: {
		startTime: string;
		endTime: string;
	};
}

interface Emits {
	(e: 'update:modelValue', value: { startTime: string; endTime: string }): void;
	(e: 'slot-selected', slot: SessionSlot): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { service } = useCool();

// 响应式数据
const selectedDate = ref(new Date());
const selectedThemeId = ref<number>();
const sessionSlots = ref<SessionSlot[]>([]);
const loading = ref(false);
const generating = ref(false);

// 提示框相关
const tooltipVisible = ref(false);
const tooltipData = ref<SessionSlot | null>(null);
const tooltipRef = ref<HTMLElement>();
const tooltipStyle = ref({});

// 计算属性
const formattedDate = computed(() => {
	return selectedDate.value.toISOString().split('T')[0];
});

// 方法
const disabledDate = (time: Date) => {
	return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
};

const onDateChange = () => {
	loadSessionPlan();
};

const onThemeChange = () => {
	loadSessionPlan();
};

const loadSessionPlan = async () => {
	if (!selectedThemeId.value) return;
	
	loading.value = true;
	try {
		const res = await service.game.session.getSessionPlan({
			date: formattedDate.value,
			themeId: selectedThemeId.value
		});
		sessionSlots.value = res?.slots || [];
	} catch (error) {
		console.error('加载场次计划失败:', error);
		ElMessage.error('加载场次计划失败');
	} finally {
		loading.value = false;
	}
};

const generatePlan = async () => {
	if (!selectedThemeId.value) {
		ElMessage.warning('请先选择主题');
		return;
	}
	
	generating.value = true;
	try {
		// 获取主题参数和营业时间
		const [themeParams, businessHours] = await Promise.all([
			service.game.sessionSetting.getThemeSessionParams(selectedThemeId.value),
			service.game.sessionSetting.getBusinessHours()
		]);
		
		const dayOfWeek = selectedDate.value.getDay();
		const todayHours = businessHours.find((h: any) => h.dayOfWeek === dayOfWeek);
		
		if (!todayHours || !todayHours.isActive) {
			ElMessage.warning('选择的日期为休息日');
			return;
		}
		
		await service.game.session.generateSessionPlan({
			themeId: selectedThemeId.value,
			date: formattedDate.value,
			businessHours: {
				start: todayHours.startTime,
				end: todayHours.endTime
			},
			gameDuration: themeParams?.gameDuration || 90,
			intervalTime: themeParams?.intervalTime || 15
		});
		
		ElMessage.success('场次计划生成成功');
		loadSessionPlan();
	} catch (error) {
		console.error('生成场次计划失败:', error);
		ElMessage.error('生成场次计划失败');
	} finally {
		generating.value = false;
	}
};

const refreshPlan = () => {
	loadSessionPlan();
};

const getSlotClass = (slot: SessionSlot) => {
	const baseClass = 'session-slot';
	const statusClasses = {
		0: 'available',
		1: 'reserved',
		2: 'ongoing',
		3: 'closed',
		4: 'maintenance'
	};
	return `${baseClass} ${statusClasses[slot.status] || 'unknown'}`;
};

const getStatusText = (status: number) => {
	const statusTexts = {
		0: '可预约',
		1: '已预约',
		2: '进行中',
		3: '已关闭',
		4: '维护中'
	};
	return statusTexts[status] || '未知';
};

const formatTime = (time: string) => {
	return new Date(time).toLocaleTimeString('zh-CN', {
		hour: '2-digit',
		minute: '2-digit'
	});
};

const onSlotClick = (slot: SessionSlot) => {
	if (slot.status !== 0) return; // 只有可用状态才能选择
	
	emit('update:modelValue', {
		startTime: slot.startTime,
		endTime: slot.endTime
	});
	emit('slot-selected', slot);
};

const showTooltip = (event: MouseEvent, slot: SessionSlot) => {
	tooltipData.value = slot;
	tooltipVisible.value = true;
	
	nextTick(() => {
		const rect = (event.target as HTMLElement).getBoundingClientRect();
		tooltipStyle.value = {
			left: rect.left + rect.width / 2 + 'px',
			top: rect.top - 10 + 'px',
			transform: 'translate(-50%, -100%)'
		};
	});
};

const hideTooltip = () => {
	tooltipVisible.value = false;
	tooltipData.value = null;
};

// 初始化
onMounted(() => {
	if (props.themeOptions.length > 0) {
		selectedThemeId.value = props.themeOptions[0].id;
		loadSessionPlan();
	}
});

// 监听主题选项变化
const updateThemeOptions = () => {
	if (props.themeOptions.length > 0 && !selectedThemeId.value) {
		selectedThemeId.value = props.themeOptions[0].id;
		loadSessionPlan();
	}
};

// 暴露方法给父组件
defineExpose({
	loadSessionPlan,
	generatePlan
});
</script>

<style lang="scss" scoped>
.session-grid {
	.grid-header {
		display: flex;
		align-items: center;
		gap: 16px;
		margin-bottom: 20px;
		padding: 16px;
		background: #f8f9fa;
		border-radius: 8px;

		.actions {
			margin-left: auto;
		}
	}

	.grid-legend {
		display: flex;
		gap: 20px;
		margin-bottom: 16px;
		padding: 12px 16px;
		background: #fff;
		border: 1px solid #e4e7ed;
		border-radius: 6px;

		.legend-item {
			display: flex;
			align-items: center;
			gap: 6px;
			font-size: 14px;

			.legend-color {
				width: 16px;
				height: 16px;
				border-radius: 4px;

				&.available {
					background: #67c23a;
				}

				&.reserved {
					background: #909399;
				}

				&.ongoing {
					background: #409eff;
				}

				&.maintenance {
					background: #e6a23c;
				}

				&.closed {
					background: #f56c6c;
				}
			}
		}
	}

	.session-slots {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
		gap: 12px;
		min-height: 200px;

		.session-slot {
			padding: 12px;
			border: 2px solid transparent;
			border-radius: 8px;
			cursor: pointer;
			transition: all 0.3s ease;
			position: relative;

			.slot-time {
				font-weight: bold;
				font-size: 14px;
				margin-bottom: 8px;
			}

			.slot-info {
				font-size: 12px;

				.slot-status {
					margin-bottom: 4px;
				}

				.slot-players {
					color: #666;
				}
			}

			&.available {
				background: #f0f9ff;
				border-color: #67c23a;

				&:hover {
					background: #e1f5fe;
					border-color: #409eff;
					transform: translateY(-2px);
					box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
				}
			}

			&.reserved {
				background: #f5f5f5;
				border-color: #909399;
				cursor: not-allowed;
				color: #666;
			}

			&.ongoing {
				background: #e3f2fd;
				border-color: #409eff;
				cursor: not-allowed;

				&::after {
					content: '进行中';
					position: absolute;
					top: 4px;
					right: 4px;
					background: #409eff;
					color: white;
					font-size: 10px;
					padding: 2px 6px;
					border-radius: 10px;
				}
			}

			&.maintenance {
				background: #fdf6ec;
				border-color: #e6a23c;
				cursor: not-allowed;
				color: #e6a23c;
			}

			&.closed {
				background: #fef0f0;
				border-color: #f56c6c;
				cursor: not-allowed;
				color: #f56c6c;
			}
		}
	}
}

.session-tooltip {
	position: fixed;
	z-index: 9999;
	background: rgba(0, 0, 0, 0.8);
	color: white;
	padding: 12px;
	border-radius: 6px;
	font-size: 12px;
	max-width: 200px;
	pointer-events: none;

	.tooltip-title {
		font-weight: bold;
		margin-bottom: 8px;
		border-bottom: 1px solid rgba(255, 255, 255, 0.3);
		padding-bottom: 4px;
	}

	.tooltip-content {
		div {
			margin-bottom: 4px;

			&:last-child {
				margin-bottom: 0;
			}
		}

		.tooltip-hint {
			color: #67c23a;
			font-style: italic;
			margin-top: 8px;
		}
	}
}

:deep(.el-empty) {
	padding: 40px 0;
}
</style>
