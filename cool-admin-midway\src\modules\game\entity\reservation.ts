import { BaseEntity } from '../../base/entity/base';
import { Column, Entity, Index, ManyToOne, JoinColumn } from 'typeorm';
import { GameThemeEntity } from './theme';
import { GameMemberEntity } from './member';

/**
 * 游戏预约实体
 */
@Entity('game_reservation')
export class GameReservationEntity extends BaseEntity {
  @Column({ comment: '预约号', length: 20, unique: true })
  reservationNo: string;

  @Index()
  @Column({ comment: '主题ID' })
  themeId: number;

  @Column({ comment: '预约人姓名', length: 50, nullable: true })
  customerName: string;

  @Index()
  @Column({ comment: '联系电话', length: 20 })
  phone: string;

  @Column({ comment: '预约人数' })
  playerCount: number;

  @Column({ comment: '预约日期', type: 'date' })
  reservationDate: Date;

  @Column({ comment: '预约开始时间', type: 'datetime' })
  startTime: Date;

  @Column({ comment: '预约结束时间', type: 'datetime' })
  endTime: Date;

  @Column({
    comment: '预约价格',
    type: 'decimal',
    precision: 10,
    scale: 2, nullable: true
  })
  price: number;

  @Column({ comment: '支付方式 1-现金 2-会员余额 3-第三方券', default: 1 })
  paymentType: number;

  @Column({ comment: '支付状态 0-未支付 1-已支付', default: 0 })
  paymentStatus: number;

  @Column({ comment: '会员ID', nullable: true })
  memberId: number;

  @Column({ comment: '第三方券码', nullable: true, length: 100 })
  couponCode: string;

  @Column({ comment: '预约状态 0-待确认 1-已确认 2-已完成 3-已取消', default: 0 })
  status: number;

  @Column({ comment: '备注', nullable: true })
  remark: string;

  @Column({ comment: '操作员ID', nullable: true })
  operatorId: number;

  @Column({ comment: '取消原因', nullable: true })
  cancelReason: string;

  @Column({ comment: '取消时间', type: 'datetime', nullable: true })
  cancelTime: Date;

  // 关系定义
  @ManyToOne(() => GameThemeEntity)
  @JoinColumn({ name: 'themeId' })
  theme: GameThemeEntity;

  @ManyToOne(() => GameMemberEntity)
  @JoinColumn({ name: 'memberId' })
  member: GameMemberEntity;
}
