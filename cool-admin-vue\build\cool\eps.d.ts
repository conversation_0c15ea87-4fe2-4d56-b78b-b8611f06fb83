declare namespace Eps {
	interface BaseSysDepartmentEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface BaseSysLogEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface BaseSysMenuEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface BaseSysParamEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface BaseSysRoleEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface BaseSysUserEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface DemoGoodsEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface DictInfoEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface DictTypeEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface GameCouponEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface GameMemberEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface GameReservationEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface GameSessionEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface GameThemeEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PluginInfoEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface RecycleDataEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface SpaceInfoEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface SpaceTypeEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface TaskInfoEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface UserAddressEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface UserInfoEntity {
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	type json = any;

	interface PagePagination {
		size: number;
		page: number;
		total: number;
		[key: string]: any;
	}

	interface PageResponse<T> {
		pagination: PagePagination;
		list: T[];
		[key: string]: any;
	}

	interface BaseSysLogPageResponse {
		pagination: PagePagination;
		list: BaseSysLogEntity[];
	}

	interface BaseSysMenuPageResponse {
		pagination: PagePagination;
		list: BaseSysMenuEntity[];
	}

	interface BaseSysParamPageResponse {
		pagination: PagePagination;
		list: BaseSysParamEntity[];
	}

	interface BaseSysRolePageResponse {
		pagination: PagePagination;
		list: BaseSysRoleEntity[];
	}

	interface BaseSysUserPageResponse {
		pagination: PagePagination;
		list: BaseSysUserEntity[];
	}

	interface DemoGoodsPageResponse {
		pagination: PagePagination;
		list: DemoGoodsEntity[];
	}

	interface DictInfoPageResponse {
		pagination: PagePagination;
		list: DictInfoEntity[];
	}

	interface DictTypePageResponse {
		pagination: PagePagination;
		list: DictTypeEntity[];
	}

	interface GameCouponPageResponse {
		pagination: PagePagination;
		list: GameCouponEntity[];
	}

	interface GameMemberPageResponse {
		pagination: PagePagination;
		list: GameMemberEntity[];
	}

	interface GameReservationPageResponse {
		pagination: PagePagination;
		list: GameReservationEntity[];
	}

	interface GameSessionPageResponse {
		pagination: PagePagination;
		list: GameSessionEntity[];
	}

	interface GameThemePageResponse {
		pagination: PagePagination;
		list: GameThemeEntity[];
	}

	interface PluginInfoPageResponse {
		pagination: PagePagination;
		list: PluginInfoEntity[];
	}

	interface RecycleDataPageResponse {
		pagination: PagePagination;
		list: RecycleDataEntity[];
	}

	interface SpaceInfoPageResponse {
		pagination: PagePagination;
		list: SpaceInfoEntity[];
	}

	interface SpaceTypePageResponse {
		pagination: PagePagination;
		list: SpaceTypeEntity[];
	}

	interface TaskInfoPageResponse {
		pagination: PagePagination;
		list: TaskInfoEntity[];
	}

	interface UserAddressPageResponse {
		pagination: PagePagination;
		list: UserAddressEntity[];
	}

	interface UserInfoPageResponse {
		pagination: PagePagination;
		list: UserInfoEntity[];
	}

	interface BaseCoding {
		/**
		 * getModuleTree
		 */
		getModuleTree(data?: any): Promise<any>;

		/**
		 * createCode
		 */
		createCode(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: { getModuleTree: string; createCode: string };

		/**
		 * 权限状态
		 */
		_permission: { getModuleTree: boolean; createCode: boolean };

		request: Request;
	}

	interface BaseComm {
		/**
		 * personUpdate
		 */
		personUpdate(data?: any): Promise<any>;

		/**
		 * uploadMode
		 */
		uploadMode(data?: any): Promise<any>;

		/**
		 * permmenu
		 */
		permmenu(data?: any): Promise<any>;

		/**
		 * program
		 */
		program(data?: any): Promise<any>;

		/**
		 * person
		 */
		person(data?: any): Promise<any>;

		/**
		 * upload
		 */
		upload(data?: any): Promise<any>;

		/**
		 * logout
		 */
		logout(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			personUpdate: string;
			uploadMode: string;
			permmenu: string;
			program: string;
			person: string;
			upload: string;
			logout: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			personUpdate: boolean;
			uploadMode: boolean;
			permmenu: boolean;
			program: boolean;
			person: boolean;
			upload: boolean;
			logout: boolean;
		};

		request: Request;
	}

	interface BaseOpen {
		/**
		 * refreshToken
		 */
		refreshToken(data?: any): Promise<any>;

		/**
		 * captcha
		 */
		captcha(data?: any): Promise<any>;

		/**
		 * login
		 */
		login(data?: any): Promise<any>;

		/**
		 * html
		 */
		html(data?: any): Promise<any>;

		/**
		 * eps
		 */
		eps(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			refreshToken: string;
			captcha: string;
			login: string;
			html: string;
			eps: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			refreshToken: boolean;
			captcha: boolean;
			login: boolean;
			html: boolean;
			eps: boolean;
		};

		request: Request;
	}

	interface BaseSysDepartment {
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;

		/**
		 * update
		 */
		update(data?: any): Promise<any>;

		/**
		 * order
		 */
		order(data?: any): Promise<any>;

		/**
		 * list
		 */
		list(data?: any): Promise<BaseSysDepartmentEntity[]>;

		/**
		 * add
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: { delete: string; update: string; order: string; list: string; add: string };

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			order: boolean;
			list: boolean;
			add: boolean;
		};

		request: Request;
	}

	interface BaseSysLog {
		/**
		 * setKeep
		 */
		setKeep(data?: any): Promise<any>;

		/**
		 * getKeep
		 */
		getKeep(data?: any): Promise<any>;

		/**
		 * clear
		 */
		clear(data?: any): Promise<any>;

		/**
		 * page
		 */
		page(data?: any): Promise<BaseSysLogPageResponse>;

		/**
		 * 权限标识
		 */
		permission: { setKeep: string; getKeep: string; clear: string; page: string };

		/**
		 * 权限状态
		 */
		_permission: { setKeep: boolean; getKeep: boolean; clear: boolean; page: boolean };

		request: Request;
	}

	interface BaseSysMenu {
		/**
		 * create
		 */
		create(data?: any): Promise<any>;

		/**
		 * export
		 */
		export(data?: any): Promise<any>;

		/**
		 * import
		 */
		import(data?: any): Promise<any>;

		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;

		/**
		 * update
		 */
		update(data?: any): Promise<any>;

		/**
		 * parse
		 */
		parse(data?: any): Promise<any>;

		/**
		 * info
		 */
		info(data?: any): Promise<BaseSysMenuEntity>;

		/**
		 * list
		 */
		list(data?: any): Promise<BaseSysMenuEntity[]>;

		/**
		 * page
		 */
		page(data?: any): Promise<BaseSysMenuPageResponse>;

		/**
		 * add
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			create: string;
			export: string;
			import: string;
			delete: string;
			update: string;
			parse: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			create: boolean;
			export: boolean;
			import: boolean;
			delete: boolean;
			update: boolean;
			parse: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Request;
	}

	interface BaseSysParam {
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;

		/**
		 * update
		 */
		update(data?: any): Promise<any>;

		/**
		 * html
		 */
		html(data?: any): Promise<any>;

		/**
		 * info
		 */
		info(data?: any): Promise<BaseSysParamEntity>;

		/**
		 * page
		 */
		page(data?: any): Promise<BaseSysParamPageResponse>;

		/**
		 * add
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			html: string;
			info: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			html: boolean;
			info: boolean;
			page: boolean;
			add: boolean;
		};

		request: Request;
	}

	interface BaseSysRole {
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;

		/**
		 * update
		 */
		update(data?: any): Promise<any>;

		/**
		 * info
		 */
		info(data?: any): Promise<BaseSysRoleEntity>;

		/**
		 * list
		 */
		list(data?: any): Promise<BaseSysRoleEntity[]>;

		/**
		 * page
		 */
		page(data?: any): Promise<BaseSysRolePageResponse>;

		/**
		 * add
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Request;
	}

	interface BaseSysUser {
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;

		/**
		 * update
		 */
		update(data?: any): Promise<any>;

		/**
		 * move
		 */
		move(data?: any): Promise<any>;

		/**
		 * info
		 */
		info(data?: any): Promise<BaseSysUserEntity>;

		/**
		 * list
		 */
		list(data?: any): Promise<BaseSysUserEntity[]>;

		/**
		 * page
		 */
		page(data?: any): Promise<BaseSysUserPageResponse>;

		/**
		 * add
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			move: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			move: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Request;
	}

	interface DemoGoods {
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;

		/**
		 * update
		 */
		update(data?: any): Promise<any>;

		/**
		 * info
		 */
		info(data?: any): Promise<DemoGoodsEntity>;

		/**
		 * list
		 */
		list(data?: any): Promise<DemoGoodsEntity[]>;

		/**
		 * page
		 */
		page(data?: any): Promise<DemoGoodsPageResponse>;

		/**
		 * add
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Request;
	}

	interface DemoTenant {
		/**
		 * noTenant
		 */
		noTenant(data?: any): Promise<any>;

		/**
		 * noUse
		 */
		noUse(data?: any): Promise<any>;

		/**
		 * use
		 */
		use(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: { noTenant: string; noUse: string; use: string };

		/**
		 * 权限状态
		 */
		_permission: { noTenant: boolean; noUse: boolean; use: boolean };

		request: Request;
	}

	interface DictInfo {
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;

		/**
		 * update
		 */
		update(data?: any): Promise<any>;

		/**
		 * types
		 */
		types(data?: any): Promise<any>;

		/**
		 * data
		 */
		data(data?: any): Promise<any>;

		/**
		 * info
		 */
		info(data?: any): Promise<DictInfoEntity>;

		/**
		 * list
		 */
		list(data?: any): Promise<DictInfoEntity[]>;

		/**
		 * page
		 */
		page(data?: any): Promise<DictInfoPageResponse>;

		/**
		 * add
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			types: string;
			data: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			types: boolean;
			data: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Request;
	}

	interface DictType {
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;

		/**
		 * update
		 */
		update(data?: any): Promise<any>;

		/**
		 * info
		 */
		info(data?: any): Promise<DictTypeEntity>;

		/**
		 * list
		 */
		list(data?: any): Promise<DictTypeEntity[]>;

		/**
		 * page
		 */
		page(data?: any): Promise<DictTypePageResponse>;

		/**
		 * add
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Request;
	}

	interface GameCoupon {
		/**
		 * updateExpired
		 */
		updateExpired(data?: any): Promise<any>;

		/**
		 * usageHistory
		 */
		usageHistory(data?: any): Promise<any>;

		/**
		 * batchCreate
		 */
		batchCreate(data?: any): Promise<any>;

		/**
		 * typeStats
		 */
		typeStats(data?: any): Promise<any>;

		/**
		 * verify
		 */
		verify(data?: any): Promise<any>;

		/**
		 * create
		 */
		create(data?: any): Promise<any>;

		/**
		 * detail
		 */
		detail(data?: any): Promise<any>;

		/**
		 * export
		 */
		export(data?: any): Promise<any>;

		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;

		/**
		 * update
		 */
		update(data?: any): Promise<any>;

		/**
		 * query
		 */
		query(data?: any): Promise<any>;

		/**
		 * stats
		 */
		stats(data?: any): Promise<any>;

		/**
		 * info
		 */
		info(data?: any): Promise<GameCouponEntity>;

		/**
		 * list
		 */
		list(data?: any): Promise<GameCouponEntity[]>;

		/**
		 * page
		 */
		page(data?: any): Promise<GameCouponPageResponse>;

		/**
		 * use
		 */
		use(data?: any): Promise<any>;

		/**
		 * add
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			updateExpired: string;
			usageHistory: string;
			batchCreate: string;
			typeStats: string;
			verify: string;
			create: string;
			detail: string;
			export: string;
			delete: string;
			update: string;
			query: string;
			stats: string;
			info: string;
			list: string;
			page: string;
			use: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			updateExpired: boolean;
			usageHistory: boolean;
			batchCreate: boolean;
			typeStats: boolean;
			verify: boolean;
			create: boolean;
			detail: boolean;
			export: boolean;
			delete: boolean;
			update: boolean;
			query: boolean;
			stats: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			use: boolean;
			add: boolean;
		};

		request: Request;
	}

	interface GameDashboard {
		/**
		 * systemStatus
		 */
		systemStatus(data?: any): Promise<any>;

		/**
		 * queueStatus
		 */
		queueStatus(data?: any): Promise<any>;

		/**
		 * gameStatus
		 */
		gameStatus(data?: any): Promise<any>;

		/**
		 * themeUsage
		 */
		themeUsage(data?: any): Promise<any>;

		/**
		 * todayData
		 */
		todayData(data?: any): Promise<any>;

		/**
		 * overview
		 */
		overview(data?: any): Promise<any>;

		/**
		 * revenue
		 */
		revenue(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			systemStatus: string;
			queueStatus: string;
			gameStatus: string;
			themeUsage: string;
			todayData: string;
			overview: string;
			revenue: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			systemStatus: boolean;
			queueStatus: boolean;
			gameStatus: boolean;
			themeUsage: boolean;
			todayData: boolean;
			overview: boolean;
			revenue: boolean;
		};

		request: Request;
	}

	interface GameMember {
		/**
		 * transactionHistory
		 */
		transactionHistory(data?: any): Promise<any>;

		/**
		 * balanceHistory
		 */
		balanceHistory(data?: any): Promise<any>;

		/**
		 * checkBalance
		 */
		checkBalance(data?: any): Promise<any>;

		/**
		 * updateMember
		 */
		updateMember(data?: any): Promise<any>;

		/**
		 * upgradeLevel
		 */
		upgradeLevel(data?: any): Promise<any>;

		/**
		 * toggleStatus
		 */
		toggleStatus(data?: any): Promise<any>;

		/**
		 * findByPhone
		 */
		findByPhone(data?: any): Promise<any>;

		/**
		 * recharge
		 */
		recharge(data?: any): Promise<any>;

		/**
		 * payment
		 */
		payment(data?: any): Promise<any>;

		/**
		 * verify
		 */
		verify(data?: any): Promise<any>;

		/**
		 * create
		 */
		create(data?: any): Promise<any>;

		/**
		 * detail
		 */
		detail(data?: any): Promise<any>;

		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;

		/**
		 * update
		 */
		update(data?: any): Promise<any>;

		/**
		 * stats
		 */
		stats(data?: any): Promise<any>;

		/**
		 * query
		 */
		query(data?: any): Promise<any>;

		/**
		 * info
		 */
		info(data?: any): Promise<GameMemberEntity>;

		/**
		 * list
		 */
		list(data?: any): Promise<GameMemberEntity[]>;

		/**
		 * page
		 */
		page(data?: any): Promise<GameMemberPageResponse>;

		/**
		 * add
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			transactionHistory: string;
			balanceHistory: string;
			checkBalance: string;
			updateMember: string;
			upgradeLevel: string;
			toggleStatus: string;
			findByPhone: string;
			recharge: string;
			payment: string;
			verify: string;
			create: string;
			detail: string;
			delete: string;
			update: string;
			stats: string;
			query: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			transactionHistory: boolean;
			balanceHistory: boolean;
			checkBalance: boolean;
			updateMember: boolean;
			upgradeLevel: boolean;
			toggleStatus: boolean;
			findByPhone: boolean;
			recharge: boolean;
			payment: boolean;
			verify: boolean;
			create: boolean;
			detail: boolean;
			delete: boolean;
			update: boolean;
			stats: boolean;
			query: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Request;
	}

	interface GameReport {
		/**
		 * themeUsageTrend
		 */
		themeUsageTrend(data?: any): Promise<any>;

		/**
		 * revenueAnalysis
		 */
		revenueAnalysis(data?: any): Promise<any>;

		/**
		 * todayQuickStats
		 */
		todayQuickStats(data?: any): Promise<any>;

		/**
		 * exportDailyCSV
		 */
		exportDailyCSV(data?: any): Promise<any>;

		/**
		 * weekQuickStats
		 */
		weekQuickStats(data?: any): Promise<any>;

		/**
		 * weeklySummary
		 */
		weeklySummary(data?: any): Promise<any>;

		/**
		 * dailySummary
		 */
		dailySummary(data?: any): Promise<any>;

		/**
		 * themeRanking
		 */
		themeRanking(data?: any): Promise<any>;

		/**
		 * paymentStats
		 */
		paymentStats(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			themeUsageTrend: string;
			revenueAnalysis: string;
			todayQuickStats: string;
			exportDailyCSV: string;
			weekQuickStats: string;
			weeklySummary: string;
			dailySummary: string;
			themeRanking: string;
			paymentStats: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			themeUsageTrend: boolean;
			revenueAnalysis: boolean;
			todayQuickStats: boolean;
			exportDailyCSV: boolean;
			weekQuickStats: boolean;
			weeklySummary: boolean;
			dailySummary: boolean;
			themeRanking: boolean;
			paymentStats: boolean;
		};

		request: Request;
	}

	interface GameReservation {
		/**
		 * updatePlayerCount
		 */
		updatePlayerCount(data?: any): Promise<any>;

		/**
		 * updateReservation
		 */
		updateReservation(data?: any): Promise<any>;

		/**
		 * batchOperation
		 */
		batchOperation(data?: any): Promise<any>;

		/**
		 * complete
		 */
		complete(data?: any): Promise<any>;

		/**
		 * upcoming
		 */
		upcoming(data?: any): Promise<any>;

		/**
		 * confirm
		 */
		confirm(data?: any): Promise<any>;

		/**
		 * byPhone
		 */
		byPhone(data?: any): Promise<any>;

		/**
		 * create
		 */
		create(data?: any): Promise<any>;

		/**
		 * cancel
		 */
		cancel(data?: any): Promise<any>;

		/**
		 * detail
		 */
		detail(data?: any): Promise<any>;

		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;

		/**
		 * update
		 */
		update(data?: any): Promise<any>;

		/**
		 * query
		 */
		query(data?: any): Promise<any>;

		/**
		 * queue
		 */
		queue(data?: any): Promise<any>;

		/**
		 * info
		 */
		info(data?: any): Promise<GameReservationEntity>;

		/**
		 * list
		 */
		list(data?: any): Promise<GameReservationEntity[]>;

		/**
		 * page
		 */
		page(data?: any): Promise<GameReservationPageResponse>;

		/**
		 * add
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			updatePlayerCount: string;
			updateReservation: string;
			batchOperation: string;
			complete: string;
			upcoming: string;
			confirm: string;
			byPhone: string;
			create: string;
			cancel: string;
			detail: string;
			delete: string;
			update: string;
			query: string;
			queue: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			updatePlayerCount: boolean;
			updateReservation: boolean;
			batchOperation: boolean;
			complete: boolean;
			upcoming: boolean;
			confirm: boolean;
			byPhone: boolean;
			create: boolean;
			cancel: boolean;
			detail: boolean;
			delete: boolean;
			update: boolean;
			query: boolean;
			queue: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Request;
	}

	interface GameSession {
		/**
		 * batchOperation
		 */
		batchOperation(data?: any): Promise<any>;

		/**
		 * updateSession
		 */
		updateSession(data?: any): Promise<any>;

		/**
		 * startTimer
		 */
		startTimer(data?: any): Promise<any>;

		/**
		 * cancelGame
		 */
		cancelGame(data?: any): Promise<any>;

		/**
		 * todayStats
		 */
		todayStats(data?: any): Promise<any>;

		/**
		 * startGame
		 */
		startGame(data?: any): Promise<any>;

		/**
		 * endGame
		 */
		endGame(data?: any): Promise<any>;

		/**
		 * active
		 */
		active(data?: any): Promise<any>;

		/**
		 * create
		 */
		create(data?: any): Promise<any>;

		/**
		 * detail
		 */
		detail(data?: any): Promise<any>;

		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;

		/**
		 * update
		 */
		update(data?: any): Promise<any>;

		/**
		 * info
		 */
		info(data?: any): Promise<GameSessionEntity>;

		/**
		 * list
		 */
		list(data?: any): Promise<GameSessionEntity[]>;

		/**
		 * page
		 */
		page(data?: any): Promise<GameSessionPageResponse>;

		/**
		 * add
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			batchOperation: string;
			updateSession: string;
			startTimer: string;
			cancelGame: string;
			todayStats: string;
			startGame: string;
			endGame: string;
			active: string;
			create: string;
			detail: string;
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			batchOperation: boolean;
			updateSession: boolean;
			startTimer: boolean;
			cancelGame: boolean;
			todayStats: boolean;
			startGame: boolean;
			endGame: boolean;
			active: boolean;
			create: boolean;
			detail: boolean;
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Request;
	}

	interface GameTheme {
		/**
		 * availableTimeSlots
		 */
		availableTimeSlots(data?: any): Promise<any>;

		/**
		 * checkAvailability
		 */
		checkAvailability(data?: any): Promise<any>;

		/**
		 * cancelRestPeriod
		 */
		cancelRestPeriod(data?: any): Promise<any>;

		/**
		 * addRestPeriod
		 */
		addRestPeriod(data?: any): Promise<any>;

		/**
		 * updateTheme
		 */
		updateTheme(data?: any): Promise<any>;

		/**
		 * available
		 */
		available(data?: any): Promise<any>;

		/**
		 * status
		 */
		status(data?: any): Promise<any>;

		/**
		 * create
		 */
		create(data?: any): Promise<any>;

		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;

		/**
		 * update
		 */
		update(data?: any): Promise<any>;

		/**
		 * info
		 */
		info(data?: any): Promise<GameThemeEntity>;

		/**
		 * list
		 */
		list(data?: any): Promise<GameThemeEntity[]>;

		/**
		 * page
		 */
		page(data?: any): Promise<GameThemePageResponse>;

		/**
		 * add
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			availableTimeSlots: string;
			checkAvailability: string;
			cancelRestPeriod: string;
			addRestPeriod: string;
			updateTheme: string;
			available: string;
			status: string;
			create: string;
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			availableTimeSlots: boolean;
			checkAvailability: boolean;
			cancelRestPeriod: boolean;
			addRestPeriod: boolean;
			updateTheme: boolean;
			available: boolean;
			status: boolean;
			create: boolean;
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Request;
	}

	interface PluginInfo {
		/**
		 * install
		 */
		install(data?: any): Promise<any>;

		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;

		/**
		 * update
		 */
		update(data?: any): Promise<any>;

		/**
		 * info
		 */
		info(data?: any): Promise<PluginInfoEntity>;

		/**
		 * list
		 */
		list(data?: any): Promise<PluginInfoEntity[]>;

		/**
		 * page
		 */
		page(data?: any): Promise<PluginInfoPageResponse>;

		/**
		 * add
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			install: string;
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			install: boolean;
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Request;
	}

	interface RecycleData {
		/**
		 * restore
		 */
		restore(data?: any): Promise<any>;

		/**
		 * info
		 */
		info(data?: any): Promise<RecycleDataEntity>;

		/**
		 * page
		 */
		page(data?: any): Promise<RecycleDataPageResponse>;

		/**
		 * 权限标识
		 */
		permission: { restore: string; info: string; page: string };

		/**
		 * 权限状态
		 */
		_permission: { restore: boolean; info: boolean; page: boolean };

		request: Request;
	}

	interface SpaceInfo {
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;

		/**
		 * update
		 */
		update(data?: any): Promise<any>;

		/**
		 * info
		 */
		info(data?: any): Promise<SpaceInfoEntity>;

		/**
		 * list
		 */
		list(data?: any): Promise<SpaceInfoEntity[]>;

		/**
		 * page
		 */
		page(data?: any): Promise<SpaceInfoPageResponse>;

		/**
		 * add
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Request;
	}

	interface SpaceType {
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;

		/**
		 * update
		 */
		update(data?: any): Promise<any>;

		/**
		 * info
		 */
		info(data?: any): Promise<SpaceTypeEntity>;

		/**
		 * list
		 */
		list(data?: any): Promise<SpaceTypeEntity[]>;

		/**
		 * page
		 */
		page(data?: any): Promise<SpaceTypePageResponse>;

		/**
		 * add
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Request;
	}

	interface TaskInfo {
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;

		/**
		 * update
		 */
		update(data?: any): Promise<any>;

		/**
		 * start
		 */
		start(data?: any): Promise<any>;

		/**
		 * once
		 */
		once(data?: any): Promise<any>;

		/**
		 * stop
		 */
		stop(data?: any): Promise<any>;

		/**
		 * info
		 */
		info(data?: any): Promise<TaskInfoEntity>;

		/**
		 * page
		 */
		page(data?: any): Promise<TaskInfoPageResponse>;

		/**
		 * log
		 */
		log(data?: any): Promise<any>;

		/**
		 * add
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			start: string;
			once: string;
			stop: string;
			info: string;
			page: string;
			log: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			start: boolean;
			once: boolean;
			stop: boolean;
			info: boolean;
			page: boolean;
			log: boolean;
			add: boolean;
		};

		request: Request;
	}

	interface UserAddress {
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;

		/**
		 * update
		 */
		update(data?: any): Promise<any>;

		/**
		 * info
		 */
		info(data?: any): Promise<UserAddressEntity>;

		/**
		 * list
		 */
		list(data?: any): Promise<UserAddressEntity[]>;

		/**
		 * page
		 */
		page(data?: any): Promise<UserAddressPageResponse>;

		/**
		 * add
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Request;
	}

	interface UserInfo {
		/**
		 * delete
		 */
		delete(data?: any): Promise<any>;

		/**
		 * update
		 */
		update(data?: any): Promise<any>;

		/**
		 * info
		 */
		info(data?: any): Promise<UserInfoEntity>;

		/**
		 * list
		 */
		list(data?: any): Promise<UserInfoEntity[]>;

		/**
		 * page
		 */
		page(data?: any): Promise<UserInfoPageResponse>;

		/**
		 * add
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Request;
	}

	interface RequestOptions {
		url: string;
		method?: "OPTIONS" | "GET" | "HEAD" | "POST" | "PUT" | "DELETE" | "TRACE" | "CONNECT";
		data?: any;
		params?: any;
		headers?: any;
		timeout?: number;
		[key: string]: any;
	}

	type Request = (options: RequestOptions) => Promise<any>;

	type Service = {
		request: Request;

		base: {
			coding: BaseCoding;
			comm: BaseComm;
			open: BaseOpen;
			sys: {
				department: BaseSysDepartment;
				log: BaseSysLog;
				menu: BaseSysMenu;
				param: BaseSysParam;
				role: BaseSysRole;
				user: BaseSysUser;
			};
		};
		demo: { goods: DemoGoods; tenant: DemoTenant };
		dict: { info: DictInfo; type: DictType };
		game: {
			coupon: GameCoupon;
			dashboard: GameDashboard;
			member: GameMember;
			report: GameReport;
			reservation: GameReservation;
			session: GameSession;
			theme: GameTheme;
		};
		plugin: { info: PluginInfo };
		recycle: { data: RecycleData };
		space: { info: SpaceInfo; type: SpaceType };
		task: { info: TaskInfo };
		user: { address: UserAddress; info: UserInfo };
	};
}
