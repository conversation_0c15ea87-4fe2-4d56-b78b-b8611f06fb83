<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn />
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />
			<cl-flex1 />
			<!-- 关键字搜索 -->
			<cl-search-key />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table">
				<!-- 主题名称 -->
				<template #column-themeName="{ scope }">
					<span style="font-weight: bold;">{{ scope.row.theme?.name || '-' }}</span>
				</template>

				<!-- 状态 -->
				<template #column-status="{ scope }">
					<el-tag :type="getStatusType(scope.row.status)">
						{{ getStatusText(scope.row.status) }}
					</el-tag>
				</template>

				<!-- 预约时间 -->
				<template #column-reservationTime="{ scope }">
					<div>
						<div><strong>开始:</strong> {{ formatTime(scope.row.startTime) }}</div>
						<div><strong>结束:</strong> {{ formatTime(scope.row.endTime) }}</div>
					</div>
				</template>

				<!-- 联系信息 -->
				<template #column-contact="{ scope }">
					<div>
						<div><strong>姓名:</strong> {{ scope.row.customerName }}</div>
						<div><strong>电话:</strong> {{ scope.row.customerPhone }}</div>
					</div>
				</template>

				<!-- 操作按钮 -->
				<template #slot-btns="{ scope }">
					<el-button-group>
						<el-button size="small" @click="editReservation(scope.row)">编辑</el-button>
						<el-button 
							v-if="scope.row.status === 0"
							size="small" 
							type="success"
							@click="confirmReservation(scope.row)"
						>
							确认预约
						</el-button>
						<el-button 
							v-if="[0, 1].includes(scope.row.status)"
							size="small" 
							type="danger"
							@click="cancelReservation(scope.row)"
						>
							取消预约
						</el-button>
						<el-button 
							v-if="scope.row.status === 1"
							size="small" 
							type="primary"
							@click="createSession(scope.row)"
						>
							创建场次
						</el-button>
					</el-button-group>
				</template>
			</cl-table>
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" width="1200px">
			<template #slot-theme="{ scope }">
				<el-select
					v-model="scope.themeId"
					placeholder="请选择主题"
					style="width: 100%"
					@change="onThemeChange"
				>
					<el-option
						v-for="theme in themeOptions"
						:key="theme.id"
						:label="`${theme.name} (时长: ${theme.duration}分钟)`"
						:value="theme.id"
					/>
				</el-select>
			</template>

			<template #slot-member="{ scope }">
				<el-select
					v-model="scope.memberId"
					placeholder="选择会员（可选）"
					style="width: 100%"
					clearable
					filterable
					remote
					:remote-method="searchMembers"
					:loading="memberLoading"
				>
					<el-option
						v-for="member in memberOptions"
						:key="member.id"
						:label="`${member.name} (${member.phone})`"
						:value="member.id"
					/>
				</el-select>
			</template>

			<template #slot-smartTimePicker="{ scope }">
				<SmartTimePicker
					ref="smartTimePickerRef"
					:theme-options="themeOptions"
					v-model="selectedTimeSlot"
					@theme-change="onSmartThemeChange"
				/>
			</template>

			<template #slot-batchActions>
				<div class="batch-actions">
					<el-button type="primary" @click="showBatchStatusDialog">批量设置状态</el-button>
					<el-button @click="exportReservations">导出预约</el-button>
				</div>
			</template>
		</cl-upsert>

		<!-- 批量状态设置对话框 -->
		<el-dialog v-model="batchStatusDialogVisible" title="批量设置状态" width="400px">
			<el-form :model="batchStatusForm" label-width="80px">
				<el-form-item label="选择状态" required>
					<el-select v-model="batchStatusForm.status" placeholder="请选择状态" style="width: 100%">
						<el-option label="待确认" :value="0" />
						<el-option label="已确认" :value="1" />
						<el-option label="已完成" :value="2" />
						<el-option label="已取消" :value="3" />
					</el-select>
				</el-form-item>
				<el-form-item label="备注">
					<el-input
						v-model="batchStatusForm.remark"
						type="textarea"
						placeholder="请输入备注"
						:rows="3"
					/>
				</el-form-item>
			</el-form>
			<template #footer>
				<el-button @click="batchStatusDialogVisible = false">取消</el-button>
				<el-button type="primary" @click="executeBatchStatus">确定</el-button>
			</template>
		</el-dialog>
	</cl-crud>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useCrud, useTable, useUpsert } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { ElMessage, ElMessageBox } from 'element-plus';
import SmartTimePicker from '../../components/SmartTimePicker.vue';

defineOptions({
	name: 'game-reservation'
});

const { service } = useCool();

// 主题选项
const themeOptions = ref<any[]>([]);
// 会员选项
const memberOptions = ref<any[]>([]);
// 会员搜索加载状态
const memberLoading = ref(false);
// 智能时间选择器引用
const smartTimePickerRef = ref();
// 选中的时间段
const selectedTimeSlot = ref<{ startTime: string; endTime: string }>();
// 批量状态对话框
const batchStatusDialogVisible = ref(false);
// 批量状态表单
const batchStatusForm = ref({
	status: 0,
	remark: ''
});

// cl-crud 配置
const Crud = useCrud(
	{
		service: service.game.reservation
	},
	(app) => {
		app.refresh();
		loadThemeOptions();
	}
);

// cl-table 配置
const Table = useTable({
	columns: [
		{
			type: 'selection',
			width: 60
		},
		{
			label: '主题名称',
			prop: 'themeName',
			minWidth: 150
		},
		{
			label: '联系信息',
			prop: 'contact',
			width: 180
		},
		{
			label: '游戏人数',
			prop: 'playerCount',
			width: 100
		},
		{
			label: '预约时间',
			prop: 'reservationTime',
			width: 200
		},
		{
			label: '状态',
			prop: 'status',
			width: 100
		},
		{
			label: '备注',
			prop: 'remark',
			minWidth: 150,
			showOverflowTooltip: true
		},
		{
			label: '创建时间',
			prop: 'createTime',
			width: 160,
			sortable: 'desc'
		},
		{
			type: 'op',
			buttons: ['slot-btns'],
			width: 280
		}
	]
});

// cl-upsert 配置
const Upsert = useUpsert({
	items: [
		{
			label: '选择主题',
			prop: 'themeId',
			required: true,
			component: {
				name: 'slot-theme'
			}
		},
		{
			label: '选择会员',
			prop: 'memberId',
			component: {
				name: 'slot-member'
			}
		},
		{
			label: '客户姓名',
			prop: 'customerName',
			component: {
				name: 'el-input',
				props: {
					placeholder: '请输入客户姓名'
				}
			}
		},
		{
			label: '客户电话',
			prop: 'customerPhone',
			required: true,
			component: {
				name: 'el-input',
				props: {
					placeholder: '请输入客户电话'
				}
			}
		},
		{
			label: '游戏人数',
			prop: 'playerCount',
			required: true,
			component: {
				name: 'el-input-number',
				props: {
					min: 1,
					max: 20,
					placeholder: '游戏人数'
				}
			}
		},
		{
			label: '智能时间选择',
			prop: 'timeSlot',
			span: 24,
			component: {
				name: 'slot-smartTimePicker'
			}
		},
		{
			label: '备注',
			prop: 'remark',
			component: {
				name: 'el-input',
				props: {
					type: 'textarea',
					rows: 3,
					placeholder: '请输入备注信息'
				}
			}
		}
	]
});

// 加载主题选项
const loadThemeOptions = async () => {
	try {
		const res = await service.game.theme.list();
		themeOptions.value = res || [];
	} catch (error) {
		console.error('加载主题选项失败:', error);
	}
};

// 搜索会员
const searchMembers = async (query: string) => {
	if (!query) {
		memberOptions.value = [];
		return;
	}

	memberLoading.value = true;
	try {
		const res = await service.game.member.page({
			keyword: query,
			size: 10
		});
		memberOptions.value = res?.list || [];
	} catch (error) {
		console.error('搜索会员失败:', error);
	} finally {
		memberLoading.value = false;
	}
};

// 主题变化处理
const onThemeChange = (themeId: number) => {
	const theme = themeOptions.value.find((t: any) => t.id === themeId);
	if (theme) {
		console.log('选择的主题:', theme);
	}
};

// 智能时间选择器主题变化处理
const onSmartThemeChange = (themeId: number) => {
	// 同步更新表单中的主题ID
	if (Upsert.value?.form) {
		Upsert.value.form.themeId = themeId;
	}
};

// 显示批量状态对话框
const showBatchStatusDialog = () => {
	const selection = Table.value?.selection;
	if (!selection || selection.length === 0) {
		ElMessage.warning('请先选择要操作的预约');
		return;
	}
	batchStatusDialogVisible.value = true;
};

// 执行批量状态更新
const executeBatchStatus = async () => {
	const selection = Table.value?.selection;
	if (!selection || selection.length === 0) {
		ElMessage.warning('请先选择要操作的预约');
		return;
	}

	try {
		const ids = selection.map((item: any) => item.id);
		await service.game.reservation.batchUpdateStatus({
			ids,
			status: batchStatusForm.value.status,
			remark: batchStatusForm.value.remark
		});

		ElMessage.success('批量更新成功');
		batchStatusDialogVisible.value = false;
		Crud.value?.refresh();
	} catch (error) {
		ElMessage.error('批量更新失败');
	}
};

// 导出预约
const exportReservations = async () => {
	try {
		const selection = Table.value?.selection;
		const params = selection && selection.length > 0
			? { ids: selection.map((item: any) => item.id) }
			: {};

		await service.game.reservation.export(params);
		ElMessage.success('导出成功');
	} catch (error) {
		ElMessage.error('导出失败');
	}
};

// 格式化时间
const formatTime = (time: string) => {
	if (!time) return '-';
	return new Date(time).toLocaleString('zh-CN');
};

// 获取状态类型
const getStatusType = (status: number) => {
	const typeMap = {
		0: 'warning',    // 待确认
		1: 'success',    // 已确认
		2: 'info',       // 已完成
		3: 'danger'      // 已取消
	};
	return typeMap[status] || 'info';
};

// 获取状态文本
const getStatusText = (status: number) => {
	const textMap = {
		0: '待确认',
		1: '已确认',
		2: '已完成',
		3: '已取消'
	};
	return textMap[status] || '未知';
};

// 编辑预约
const editReservation = (row: any) => {
	Upsert.value?.open({
		type: 'update',
		data: row
	});
};

// 确认预约
const confirmReservation = async (row: any) => {
	try {
		await ElMessageBox.confirm(
			`确定要确认"${row.customerName}"的预约吗？`,
			'确认预约',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}
		);

		await service.game.reservation.confirmReservation({ id: row.id });
		ElMessage.success('预约已确认');
		Crud.value?.refresh();
	} catch (error) {
		if (error !== 'cancel') {
			ElMessage.error('确认预约失败');
		}
	}
};

// 取消预约
const cancelReservation = async (row: any) => {
	try {
		await ElMessageBox.confirm(
			`确定要取消"${row.customerName}"的预约吗？`,
			'取消预约',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}
		);

		await service.game.reservation.cancelReservation({ id: row.id });
		ElMessage.success('预约已取消');
		Crud.value?.refresh();
	} catch (error) {
		if (error !== 'cancel') {
			ElMessage.error('取消预约失败');
		}
	}
};

// 创建场次
const createSession = async (row: any) => {
	try {
		await ElMessageBox.confirm(
			`确定要为"${row.customerName}"的预约创建游戏场次吗？`,
			'创建场次',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'info'
			}
		);

		// 这里应该调用创建场次的API
		await service.game.session.add({
			themeId: row.themeId,
			customerName: row.customerName,
			customerPhone: row.customerPhone,
			playerCount: row.playerCount,
			startTime: row.startTime,
			reservationId: row.id
		});

		ElMessage.success('游戏场次已创建');
		Crud.value?.refresh();
	} catch (error) {
		if (error !== 'cancel') {
			ElMessage.error('创建场次失败');
		}
	}
};
</script>

<style lang="scss" scoped>
.el-button-group {
	.el-button {
		margin-left: 0;
	}
}
</style>
