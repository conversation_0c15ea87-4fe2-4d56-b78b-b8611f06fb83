{"version": 3, "sources": ["../../vuedraggable/src/vuedraggable.js", "../../vuedraggable/src/util/htmlHelper.js", "../../vuedraggable/src/util/console.js", "../../vuedraggable/src/util/string.js", "../../vuedraggable/src/core/sortableEvents.js", "../../vuedraggable/src/util/tags.js", "../../vuedraggable/src/core/componentBuilderHelper.js", "../../vuedraggable/src/core/componentStructure.js", "../../vuedraggable/src/core/renderHelper.js"], "sourcesContent": ["import Sortable from \"sortablejs\";\r\nimport { insertNodeAt, removeNode } from \"./util/htmlHelper\";\r\nimport { console } from \"./util/console\";\r\nimport {\r\n  getComponentAttributes,\r\n  createSortableOption,\r\n  getValidSortableEntries\r\n} from \"./core/componentBuilderHelper\";\r\nimport { computeComponentStructure } from \"./core/renderHelper\";\r\nimport { events } from \"./core/sortableEvents\";\r\nimport { h, defineComponent, nextTick } from \"vue\";\r\n\r\nfunction emit(evtName, evtData) {\r\n  nextTick(() => this.$emit(evtName.toLowerCase(), evtData));\r\n}\r\n\r\nfunction manage(evtName) {\r\n  return (evtData, originalElement) => {\r\n    if (this.realList !== null) {\r\n      return this[`onDrag${evtName}`](evtData, originalElement);\r\n    }\r\n  };\r\n}\r\n\r\nfunction manageAndEmit(evtName) {\r\n  const delegateCallBack = manage.call(this, evtName);\r\n  return (evtData, originalElement) => {\r\n    delegateCallBack.call(this, evtData, originalElement);\r\n    emit.call(this, evtName, evtData);\r\n  };\r\n}\r\n\r\nlet draggingElement = null;\r\n\r\nconst props = {\r\n  list: {\r\n    type: Array,\r\n    required: false,\r\n    default: null\r\n  },\r\n  modelValue: {\r\n    type: Array,\r\n    required: false,\r\n    default: null\r\n  },\r\n  itemKey: {\r\n    type: [String, Function],\r\n    required: true\r\n  },\r\n  clone: {\r\n    type: Function,\r\n    default: original => {\r\n      return original;\r\n    }\r\n  },\r\n  tag: {\r\n    type: String,\r\n    default: \"div\"\r\n  },\r\n  move: {\r\n    type: Function,\r\n    default: null\r\n  },\r\n  componentData: {\r\n    type: Object,\r\n    required: false,\r\n    default: null\r\n  }\r\n};\r\n\r\nconst emits = [\r\n  \"update:modelValue\",\r\n  \"change\",\r\n  ...[...events.manageAndEmit, ...events.emit].map(evt => evt.toLowerCase())\r\n];\r\n\r\nconst draggableComponent = defineComponent({\r\n  name: \"draggable\",\r\n\r\n  inheritAttrs: false,\r\n\r\n  props,\r\n\r\n  emits,\r\n\r\n  data() {\r\n    return {\r\n      error: false\r\n    };\r\n  },\r\n\r\n  render() {\r\n    try {\r\n      this.error = false;\r\n      const { $slots, $attrs, tag, componentData, realList, getKey } = this;\r\n      const componentStructure = computeComponentStructure({\r\n        $slots,\r\n        tag,\r\n        realList,\r\n        getKey\r\n      });\r\n      this.componentStructure = componentStructure;\r\n      const attributes = getComponentAttributes({ $attrs, componentData });\r\n      return componentStructure.render(h, attributes);\r\n    } catch (err) {\r\n      this.error = true;\r\n      return h(\"pre\", { style: { color: \"red\" } }, err.stack);\r\n    }\r\n  },\r\n\r\n  created() {\r\n    if (this.list !== null && this.modelValue !== null) {\r\n      console.error(\r\n        \"modelValue and list props are mutually exclusive! Please set one or another.\"\r\n      );\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    if (this.error) {\r\n      return;\r\n    }\r\n\r\n    const { $attrs, $el, componentStructure } = this;\r\n    componentStructure.updated();\r\n\r\n    const sortableOptions = createSortableOption({\r\n      $attrs,\r\n      callBackBuilder: {\r\n        manageAndEmit: event => manageAndEmit.call(this, event),\r\n        emit: event => emit.bind(this, event),\r\n        manage: event => manage.call(this, event)\r\n      }\r\n    });\r\n    const targetDomElement = $el.nodeType === 1 ? $el : $el.parentElement;\r\n    this._sortable = new Sortable(targetDomElement, sortableOptions);\r\n    this.targetDomElement = targetDomElement;\r\n    targetDomElement.__draggable_component__ = this;\r\n  },\r\n\r\n  updated() {\r\n    this.componentStructure.updated();\r\n  },\r\n\r\n  beforeUnmount() {\r\n    if (this._sortable !== undefined) this._sortable.destroy();\r\n  },\r\n\r\n  computed: {\r\n    realList() {\r\n      const { list } = this;\r\n      return list ? list : this.modelValue;\r\n    },\r\n\r\n    getKey() {\r\n      const { itemKey } = this;\r\n      if (typeof itemKey === \"function\") {\r\n        return itemKey;\r\n      }\r\n      return element => element[itemKey];\r\n    }\r\n  },\r\n\r\n  watch: {\r\n    $attrs: {\r\n      handler(newOptionValue) {\r\n        const { _sortable } = this;\r\n        if (!_sortable) return;\r\n        getValidSortableEntries(newOptionValue).forEach(([key, value]) => {\r\n          _sortable.option(key, value);\r\n        });\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    getUnderlyingVm(domElement) {\r\n      return this.componentStructure.getUnderlyingVm(domElement) || null;\r\n    },\r\n\r\n    getUnderlyingPotencialDraggableComponent(htmElement) {\r\n      //TODO check case where you need to see component children\r\n      return htmElement.__draggable_component__;\r\n    },\r\n\r\n    emitChanges(evt) {\r\n      nextTick(() => this.$emit(\"change\", evt));\r\n    },\r\n\r\n    alterList(onList) {\r\n      if (this.list) {\r\n        onList(this.list);\r\n        return;\r\n      }\r\n      const newList = [...this.modelValue];\r\n      onList(newList);\r\n      this.$emit(\"update:modelValue\", newList);\r\n    },\r\n\r\n    spliceList() {\r\n      const spliceList = list => list.splice(...arguments);\r\n      this.alterList(spliceList);\r\n    },\r\n\r\n    updatePosition(oldIndex, newIndex) {\r\n      const updatePosition = list =>\r\n        list.splice(newIndex, 0, list.splice(oldIndex, 1)[0]);\r\n      this.alterList(updatePosition);\r\n    },\r\n\r\n    getRelatedContextFromMoveEvent({ to, related }) {\r\n      const component = this.getUnderlyingPotencialDraggableComponent(to);\r\n      if (!component) {\r\n        return { component };\r\n      }\r\n      const list = component.realList;\r\n      const context = { list, component };\r\n      if (to !== related && list) {\r\n        const destination = component.getUnderlyingVm(related) || {};\r\n        return { ...destination, ...context };\r\n      }\r\n      return context;\r\n    },\r\n\r\n    getVmIndexFromDomIndex(domIndex) {\r\n      return this.componentStructure.getVmIndexFromDomIndex(\r\n        domIndex,\r\n        this.targetDomElement\r\n      );\r\n    },\r\n\r\n    onDragStart(evt) {\r\n      this.context = this.getUnderlyingVm(evt.item);\r\n      evt.item._underlying_vm_ = this.clone(this.context.element);\r\n      draggingElement = evt.item;\r\n    },\r\n\r\n    onDragAdd(evt) {\r\n      const element = evt.item._underlying_vm_;\r\n      if (element === undefined) {\r\n        return;\r\n      }\r\n      removeNode(evt.item);\r\n      const newIndex = this.getVmIndexFromDomIndex(evt.newIndex);\r\n      this.spliceList(newIndex, 0, element);\r\n      const added = { element, newIndex };\r\n      this.emitChanges({ added });\r\n    },\r\n\r\n    onDragRemove(evt) {\r\n      insertNodeAt(this.$el, evt.item, evt.oldIndex);\r\n      if (evt.pullMode === \"clone\") {\r\n        removeNode(evt.clone);\r\n        return;\r\n      }\r\n      const { index: oldIndex, element } = this.context;\r\n      this.spliceList(oldIndex, 1);\r\n      const removed = { element, oldIndex };\r\n      this.emitChanges({ removed });\r\n    },\r\n\r\n    onDragUpdate(evt) {\r\n      removeNode(evt.item);\r\n      insertNodeAt(evt.from, evt.item, evt.oldIndex);\r\n      const oldIndex = this.context.index;\r\n      const newIndex = this.getVmIndexFromDomIndex(evt.newIndex);\r\n      this.updatePosition(oldIndex, newIndex);\r\n      const moved = { element: this.context.element, oldIndex, newIndex };\r\n      this.emitChanges({ moved });\r\n    },\r\n\r\n    computeFutureIndex(relatedContext, evt) {\r\n      if (!relatedContext.element) {\r\n        return 0;\r\n      }\r\n      const domChildren = [...evt.to.children].filter(\r\n        el => el.style[\"display\"] !== \"none\"\r\n      );\r\n      const currentDomIndex = domChildren.indexOf(evt.related);\r\n      const currentIndex = relatedContext.component.getVmIndexFromDomIndex(\r\n        currentDomIndex\r\n      );\r\n      const draggedInList = domChildren.indexOf(draggingElement) !== -1;\r\n      return draggedInList || !evt.willInsertAfter\r\n        ? currentIndex\r\n        : currentIndex + 1;\r\n    },\r\n\r\n    onDragMove(evt, originalEvent) {\r\n      const { move, realList } = this;\r\n      if (!move || !realList) {\r\n        return true;\r\n      }\r\n\r\n      const relatedContext = this.getRelatedContextFromMoveEvent(evt);\r\n      const futureIndex = this.computeFutureIndex(relatedContext, evt);\r\n      const draggedContext = {\r\n        ...this.context,\r\n        futureIndex\r\n      };\r\n      const sendEvent = {\r\n        ...evt,\r\n        relatedContext,\r\n        draggedContext\r\n      };\r\n      return move(sendEvent, originalEvent);\r\n    },\r\n\r\n    onDragEnd() {\r\n      draggingElement = null;\r\n    }\r\n  }\r\n});\r\n\r\nexport default draggableComponent;\r\n", "function removeNode(node) {\r\n  if (node.parentElement !== null) {\r\n    node.parentElement.removeChild(node);\r\n  }\r\n}\r\n\r\nfunction insertNodeAt(fatherNode, node, position) {\r\n  const refNode =\r\n    position === 0\r\n      ? fatherNode.children[0]\r\n      : fatherNode.children[position - 1].nextSibling;\r\n  fatherNode.insertBefore(node, refNode);\r\n}\r\n\r\nexport { insertNodeAt, removeNode };\r\n", "function getConsole() {\r\n  if (typeof window !== \"undefined\") {\r\n    return window.console;\r\n  }\r\n  return global.console;\r\n}\r\nconst console = getConsole();\r\n\r\nexport { console };\r\n", "function cached(fn) {\r\n  const cache = Object.create(null);\r\n  return function cachedFn(str) {\r\n    const hit = cache[str];\r\n    return hit || (cache[str] = fn(str));\r\n  };\r\n}\r\n\r\nconst regex = /-(\\w)/g;\r\nconst camelize = cached(str => str.replace(regex, (_, c) => c.toUpperCase()));\r\n\r\nexport { camelize };\r\n", "const manageAndEmit = [\"Start\", \"Add\", \"Remove\", \"Update\", \"End\"];\r\nconst emit = [\"Choose\", \"Unchoose\", \"Sort\", \"Filter\", \"Clone\"];\r\nconst manage = [\"Move\"];\r\nconst eventHandlerNames = [manage, manageAndEmit, emit]\r\n  .flatMap(events => events)\r\n  .map(evt => `on${evt}`);\r\n\r\nconst events = {\r\n  manage,\r\n  manageAndEmit,\r\n  emit\r\n};\r\n\r\nfunction isReadOnly(eventName) {\r\n  return eventHandlerNames.indexOf(eventName) !== -1;\r\n}\r\n\r\nexport { events, isReadOnly };\r\n", "const tags = [\r\n  \"a\",\r\n  \"abbr\",\r\n  \"address\",\r\n  \"area\",\r\n  \"article\",\r\n  \"aside\",\r\n  \"audio\",\r\n  \"b\",\r\n  \"base\",\r\n  \"bdi\",\r\n  \"bdo\",\r\n  \"blockquote\",\r\n  \"body\",\r\n  \"br\",\r\n  \"button\",\r\n  \"canvas\",\r\n  \"caption\",\r\n  \"cite\",\r\n  \"code\",\r\n  \"col\",\r\n  \"colgroup\",\r\n  \"data\",\r\n  \"datalist\",\r\n  \"dd\",\r\n  \"del\",\r\n  \"details\",\r\n  \"dfn\",\r\n  \"dialog\",\r\n  \"div\",\r\n  \"dl\",\r\n  \"dt\",\r\n  \"em\",\r\n  \"embed\",\r\n  \"fieldset\",\r\n  \"figcaption\",\r\n  \"figure\",\r\n  \"footer\",\r\n  \"form\",\r\n  \"h1\",\r\n  \"h2\",\r\n  \"h3\",\r\n  \"h4\",\r\n  \"h5\",\r\n  \"h6\",\r\n  \"head\",\r\n  \"header\",\r\n  \"hgroup\",\r\n  \"hr\",\r\n  \"html\",\r\n  \"i\",\r\n  \"iframe\",\r\n  \"img\",\r\n  \"input\",\r\n  \"ins\",\r\n  \"kbd\",\r\n  \"label\",\r\n  \"legend\",\r\n  \"li\",\r\n  \"link\",\r\n  \"main\",\r\n  \"map\",\r\n  \"mark\",\r\n  \"math\",\r\n  \"menu\",\r\n  \"menuitem\",\r\n  \"meta\",\r\n  \"meter\",\r\n  \"nav\",\r\n  \"noscript\",\r\n  \"object\",\r\n  \"ol\",\r\n  \"optgroup\",\r\n  \"option\",\r\n  \"output\",\r\n  \"p\",\r\n  \"param\",\r\n  \"picture\",\r\n  \"pre\",\r\n  \"progress\",\r\n  \"q\",\r\n  \"rb\",\r\n  \"rp\",\r\n  \"rt\",\r\n  \"rtc\",\r\n  \"ruby\",\r\n  \"s\",\r\n  \"samp\",\r\n  \"script\",\r\n  \"section\",\r\n  \"select\",\r\n  \"slot\",\r\n  \"small\",\r\n  \"source\",\r\n  \"span\",\r\n  \"strong\",\r\n  \"style\",\r\n  \"sub\",\r\n  \"summary\",\r\n  \"sup\",\r\n  \"svg\",\r\n  \"table\",\r\n  \"tbody\",\r\n  \"td\",\r\n  \"template\",\r\n  \"textarea\",\r\n  \"tfoot\",\r\n  \"th\",\r\n  \"thead\",\r\n  \"time\",\r\n  \"title\",\r\n  \"tr\",\r\n  \"track\",\r\n  \"u\",\r\n  \"ul\",\r\n  \"var\",\r\n  \"video\",\r\n  \"wbr\"\r\n];\r\n\r\nfunction isHtmlTag(name) {\r\n  return tags.includes(name);\r\n}\r\n\r\nfunction isTransition(name) {\r\n  return [\"transition-group\", \"TransitionGroup\"].includes(name);\r\n}\r\n\r\nfunction isHtmlAttribute(value) {\r\n  return (\r\n    [\"id\", \"class\", \"role\", \"style\"].includes(value) ||\r\n    value.startsWith(\"data-\") ||\r\n    value.startsWith(\"aria-\") ||\r\n    value.startsWith(\"on\")\r\n  );\r\n}\r\n\r\nexport { isHtmlTag, isHtmlAttribute, isTransition };\r\n", "import { camelize } from \"../util/string\";\r\nimport { events, isReadOnly } from \"./sortableEvents\";\r\nimport { isHtmlAttribute } from \"../util/tags\";\r\n\r\nfunction project(entries) {\r\n  return entries.reduce((res, [key, value]) => {\r\n    res[key] = value;\r\n    return res;\r\n  }, {});\r\n}\r\n\r\nfunction getComponentAttributes({ $attrs, componentData = {} }) {\r\n  const attributes = project(\r\n    Object.entries($attrs).filter(([key, _]) => isHtmlAttribute(key))\r\n  );\r\n  return {\r\n    ...attributes,\r\n    ...componentData\r\n  };\r\n}\r\n\r\nfunction createSortableOption({ $attrs, callBackBuilder }) {\r\n  const options = project(getValidSortableEntries($attrs));\r\n  Object.entries(callBackBuilder).forEach(([eventType, eventBuilder]) => {\r\n    events[eventType].forEach(event => {\r\n      options[`on${event}`] = eventBuilder(event);\r\n    });\r\n  });\r\n  const draggable = `[data-draggable]${options.draggable || \"\"}`;\r\n  return {\r\n    ...options,\r\n    draggable\r\n  };\r\n}\r\n\r\nfunction getValidSortableEntries(value) {\r\n  return Object.entries(value)\r\n    .filter(([key, _]) => !isHtmlAttribute(key))\r\n    .map(([key, value]) => [camelize(key), value])\r\n    .filter(([key, _]) => !isReadOnly(key));\r\n}\r\n\r\nexport {\r\n  getComponentAttributes,\r\n  createSortableOption,\r\n  getValidSortableEntries\r\n};\r\n", "const getHtmlElementFromNode = ({ el }) => el;\r\nconst addContext = (domElement, context) =>\r\n  (domElement.__draggable_context = context);\r\nconst getContext = domElement => domElement.__draggable_context;\r\n\r\nclass ComponentStructure {\r\n  constructor({\r\n    nodes: { header, default: defaultNodes, footer },\r\n    root,\r\n    realList\r\n  }) {\r\n    this.defaultNodes = defaultNodes;\r\n    this.children = [...header, ...defaultNodes, ...footer];\r\n    this.externalComponent = root.externalComponent;\r\n    this.rootTransition = root.transition;\r\n    this.tag = root.tag;\r\n    this.realList = realList;\r\n  }\r\n\r\n  get _isRootComponent() {\r\n    return this.externalComponent || this.rootTransition;\r\n  }\r\n\r\n  render(h, attributes) {\r\n    const { tag, children, _isRootComponent } = this;\r\n    const option = !_isRootComponent ? children : { default: () => children };\r\n    return h(tag, attributes, option);\r\n  }\r\n\r\n  updated() {\r\n    const { defaultNodes, realList } = this;\r\n    defaultNodes.forEach((node, index) => {\r\n      addContext(getHtmlElementFromNode(node), {\r\n        element: realList[index],\r\n        index\r\n      });\r\n    });\r\n  }\r\n\r\n  getUnderlyingVm(domElement) {\r\n    return getContext(domElement);\r\n  }\r\n\r\n  getVmIndexFromDomIndex(domIndex, element) {\r\n    const { defaultNodes } = this;\r\n    const { length } = defaultNodes;\r\n    const domChildren = element.children;\r\n    const domElement = domChildren.item(domIndex);\r\n\r\n    if (domElement === null) {\r\n      return length;\r\n    }\r\n    const context = getContext(domElement);\r\n    if (context) {\r\n      return context.index;\r\n    }\r\n\r\n    if (length === 0) {\r\n      return 0;\r\n    }\r\n    const firstDomListElement = getHtmlElementFromNode(defaultNodes[0]);\r\n    const indexFirstDomListElement = [...domChildren].findIndex(\r\n      element => element === firstDomListElement\r\n    );\r\n    return domIndex < indexFirstDomListElement ? 0 : length;\r\n  }\r\n}\r\n\r\nexport { ComponentStructure };\r\n", "import { ComponentStructure } from \"./componentStructure\";\r\nimport { isHtmlTag, isTransition } from \"../util/tags\";\r\nimport { resolveComponent, TransitionGroup } from \"vue\";\r\n\r\nfunction getSlot(slots, key) {\r\n  const slotValue = slots[key];\r\n  return slotValue ? slotValue() : [];\r\n}\r\n\r\nfunction computeNodes({ $slots, realList, getKey }) {\r\n  const normalizedList = realList || [];\r\n  const [header, footer] = [\"header\", \"footer\"].map(name =>\r\n    getSlot($slots, name)\r\n  );\r\n  const { item } = $slots;\r\n  if (!item) {\r\n    throw new Error(\"draggable element must have an item slot\");\r\n  }\r\n  const defaultNodes = normalizedList.flatMap((element, index) =>\r\n    item({ element, index }).map(node => {\r\n      node.key = getKey(element);\r\n      node.props = { ...(node.props || {}), \"data-draggable\": true };\r\n      return node;\r\n    })\r\n  );\r\n  if (defaultNodes.length !== normalizedList.length) {\r\n    throw new Error(\"Item slot must have only one child\");\r\n  }\r\n  return {\r\n    header,\r\n    footer,\r\n    default: defaultNodes\r\n  };\r\n}\r\n\r\nfunction getRootInformation(tag) {\r\n  const transition = isTransition(tag);\r\n  const externalComponent = !isHtmlTag(tag) && !transition;\r\n  return {\r\n    transition,\r\n    externalComponent,\r\n    tag: externalComponent\r\n      ? resolveComponent(tag)\r\n      : transition\r\n      ? TransitionGroup\r\n      : tag\r\n  };\r\n}\r\n\r\nfunction computeComponentStructure({ $slots, tag, realList, getKey }) {\r\n  const nodes = computeNodes({ $slots, realList, getKey });\r\n  const root = getRootInformation(tag);\r\n  return new ComponentStructure({ nodes, root, realList });\r\n}\r\n\r\nexport { computeComponentStructure };\r\n"], "mappings": ";;;;;;;;;;;;;;;AAAA;;;ACAA,SAAS,WAAW,MAAM;AACxB,MAAI,KAAK,kBAAkB,MAAM;AAC/B,SAAK,cAAc,YAAY,IAAI;AAAA,EACrC;AACF;AAEA,SAAS,aAAa,YAAY,MAAM,UAAU;AAChD,QAAM,UACJ,aAAa,IACT,WAAW,SAAS,CAAC,IACrB,WAAW,SAAS,WAAW,CAAC,EAAE;AACxC,aAAW,aAAa,MAAM,OAAO;AACvC;;;ACZA,SAAS,aAAa;AACpB,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO,OAAO;AAAA,EAChB;AACA,SAAO,OAAO;AAChB;AACA,IAAM,UAAU,WAAW;;;ACN3B,SAAS,OAAO,IAAI;AAClB,QAAM,QAAQ,uBAAO,OAAO,IAAI;AAChC,SAAO,SAAS,SAAS,KAAK;AAC5B,UAAM,MAAM,MAAM,GAAG;AACrB,WAAO,QAAQ,MAAM,GAAG,IAAI,GAAG,GAAG;AAAA,EACpC;AACF;AAEA,IAAM,QAAQ;AACd,IAAM,WAAW,OAAO,SAAO,IAAI,QAAQ,OAAO,CAAC,GAAG,MAAM,EAAE,YAAY,CAAC,CAAC;;;ACT5E,IAAM,gBAAgB,CAAC,SAAS,OAAO,UAAU,UAAU,KAAK;AAChE,IAAM,OAAO,CAAC,UAAU,YAAY,QAAQ,UAAU,OAAO;AAC7D,IAAM,SAAS,CAAC,MAAM;AACtB,IAAM,oBAAoB,CAAC,QAAQ,eAAe,IAAI,EACnD,QAAQ,CAAAA,YAAUA,OAAM,EACxB,IAAI,SAAO,KAAK,GAAG,EAAE;AAExB,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,WAAW,WAAW;AAC7B,SAAO,kBAAkB,QAAQ,SAAS,MAAM;AAClD;;;ACfA,IAAM,OAAO;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,UAAU,MAAM;AACvB,SAAO,KAAK,SAAS,IAAI;AAC3B;AAEA,SAAS,aAAa,MAAM;AAC1B,SAAO,CAAC,oBAAoB,iBAAiB,EAAE,SAAS,IAAI;AAC9D;AAEA,SAAS,gBAAgB,OAAO;AAC9B,SACE,CAAC,MAAM,SAAS,QAAQ,OAAO,EAAE,SAAS,KAAK,KAC/C,MAAM,WAAW,OAAO,KACxB,MAAM,WAAW,OAAO,KACxB,MAAM,WAAW,IAAI;AAEzB;;;ACnIA,SAAS,QAAQ,SAAS;AACxB,SAAO,QAAQ,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM;AAC3C,QAAI,GAAG,IAAI;AACX,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAEA,SAAS,uBAAuB,EAAE,QAAQ,gBAAgB,CAAC,EAAE,GAAG;AAC9D,QAAM,aAAa;AAAA,IACjB,OAAO,QAAQ,MAAM,EAAE,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,gBAAgB,GAAG,CAAC;AAAA,EAClE;AACA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACF;AAEA,SAAS,qBAAqB,EAAE,QAAQ,gBAAgB,GAAG;AACzD,QAAM,UAAU,QAAQ,wBAAwB,MAAM,CAAC;AACvD,SAAO,QAAQ,eAAe,EAAE,QAAQ,CAAC,CAAC,WAAW,YAAY,MAAM;AACrE,WAAO,SAAS,EAAE,QAAQ,WAAS;AACjC,cAAQ,KAAK,KAAK,EAAE,IAAI,aAAa,KAAK;AAAA,IAC5C,CAAC;AAAA,EACH,CAAC;AACD,QAAM,YAAY,mBAAmB,QAAQ,aAAa,EAAE;AAC5D,SAAO;AAAA,IACL,GAAG;AAAA,IACH;AAAA,EACF;AACF;AAEA,SAAS,wBAAwB,OAAO;AACtC,SAAO,OAAO,QAAQ,KAAK,EACxB,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,gBAAgB,GAAG,CAAC,EAC1C,IAAI,CAAC,CAAC,KAAKC,MAAK,MAAM,CAAC,SAAS,GAAG,GAAGA,MAAK,CAAC,EAC5C,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC;AAC1C;;;ACxCA,IAAM,yBAAyB,CAAC,EAAE,GAAG,MAAM;AAC3C,IAAM,aAAa,CAAC,YAAY,YAC7B,WAAW,sBAAsB;AACpC,IAAM,aAAa,gBAAc,WAAW;AAE5C,IAAM,qBAAN,MAAyB;AAAA,EACvB,YAAY;AAAA,IACV,OAAO,EAAE,QAAQ,SAAS,cAAc,OAAO;AAAA,IAC/C;AAAA,IACA;AAAA,EACF,GAAG;AACD,SAAK,eAAe;AACpB,SAAK,WAAW,CAAC,GAAG,QAAQ,GAAG,cAAc,GAAG,MAAM;AACtD,SAAK,oBAAoB,KAAK;AAC9B,SAAK,iBAAiB,KAAK;AAC3B,SAAK,MAAM,KAAK;AAChB,SAAK,WAAW;AAAA,EAClB;AAAA,EAEA,IAAI,mBAAmB;AACrB,WAAO,KAAK,qBAAqB,KAAK;AAAA,EACxC;AAAA,EAEA,OAAOC,IAAG,YAAY;AACpB,UAAM,EAAE,KAAK,UAAU,iBAAiB,IAAI;AAC5C,UAAM,SAAS,CAAC,mBAAmB,WAAW,EAAE,SAAS,MAAM,SAAS;AACxE,WAAOA,GAAE,KAAK,YAAY,MAAM;AAAA,EAClC;AAAA,EAEA,UAAU;AACR,UAAM,EAAE,cAAc,SAAS,IAAI;AACnC,iBAAa,QAAQ,CAAC,MAAM,UAAU;AACpC,iBAAW,uBAAuB,IAAI,GAAG;AAAA,QACvC,SAAS,SAAS,KAAK;AAAA,QACvB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EAEA,gBAAgB,YAAY;AAC1B,WAAO,WAAW,UAAU;AAAA,EAC9B;AAAA,EAEA,uBAAuB,UAAU,SAAS;AACxC,UAAM,EAAE,aAAa,IAAI;AACzB,UAAM,EAAE,OAAO,IAAI;AACnB,UAAM,cAAc,QAAQ;AAC5B,UAAM,aAAa,YAAY,KAAK,QAAQ;AAE5C,QAAI,eAAe,MAAM;AACvB,aAAO;AAAA,IACT;AACA,UAAM,UAAU,WAAW,UAAU;AACrC,QAAI,SAAS;AACX,aAAO,QAAQ;AAAA,IACjB;AAEA,QAAI,WAAW,GAAG;AAChB,aAAO;AAAA,IACT;AACA,UAAM,sBAAsB,uBAAuB,aAAa,CAAC,CAAC;AAClE,UAAM,2BAA2B,CAAC,GAAG,WAAW,EAAE;AAAA,MAChD,CAAAC,aAAWA,aAAY;AAAA,IACzB;AACA,WAAO,WAAW,2BAA2B,IAAI;AAAA,EACnD;AACF;;;AC9DA,SAAS,QAAQ,OAAO,KAAK;AAC3B,QAAM,YAAY,MAAM,GAAG;AAC3B,SAAO,YAAY,UAAU,IAAI,CAAC;AACpC;AAEA,SAAS,aAAa,EAAE,QAAQ,UAAU,OAAO,GAAG;AAClD,QAAM,iBAAiB,YAAY,CAAC;AACpC,QAAM,CAAC,QAAQ,MAAM,IAAI,CAAC,UAAU,QAAQ,EAAE;AAAA,IAAI,UAChD,QAAQ,QAAQ,IAAI;AAAA,EACtB;AACA,QAAM,EAAE,KAAK,IAAI;AACjB,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,0CAA0C;AAAA,EAC5D;AACA,QAAM,eAAe,eAAe;AAAA,IAAQ,CAAC,SAAS,UACpD,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,IAAI,UAAQ;AACnC,WAAK,MAAM,OAAO,OAAO;AACzB,WAAK,QAAQ,EAAE,GAAI,KAAK,SAAS,CAAC,GAAI,kBAAkB,KAAK;AAC7D,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACA,MAAI,aAAa,WAAW,eAAe,QAAQ;AACjD,UAAM,IAAI,MAAM,oCAAoC;AAAA,EACtD;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,SAAS;AAAA,EACX;AACF;AAEA,SAAS,mBAAmB,KAAK;AAC/B,QAAM,aAAa,aAAa,GAAG;AACnC,QAAM,oBAAoB,CAAC,UAAU,GAAG,KAAK,CAAC;AAC9C,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,KAAK,oBACD,iBAAiB,GAAG,IACpB,aACA,kBACA;AAAA,EACN;AACF;AAEA,SAAS,0BAA0B,EAAE,QAAQ,KAAK,UAAU,OAAO,GAAG;AACpE,QAAM,QAAQ,aAAa,EAAE,QAAQ,UAAU,OAAO,CAAC;AACvD,QAAM,OAAO,mBAAmB,GAAG;AACnC,SAAO,IAAI,mBAAmB,EAAE,OAAO,MAAM,SAAS,CAAC;AACzD;;;ARzCA,SAASC,MAAK,SAAS,SAAS;AAC9B,WAAS,MAAM,KAAK,MAAM,QAAQ,YAAY,GAAG,OAAO,CAAC;AAC3D;AAEA,SAASC,QAAO,SAAS;AACvB,SAAO,CAAC,SAAS,oBAAoB;AACnC,QAAI,KAAK,aAAa,MAAM;AAC1B,aAAO,KAAK,SAAS,OAAO,EAAE,EAAE,SAAS,eAAe;AAAA,IAC1D;AAAA,EACF;AACF;AAEA,SAASC,eAAc,SAAS;AAC9B,QAAM,mBAAmBD,QAAO,KAAK,MAAM,OAAO;AAClD,SAAO,CAAC,SAAS,oBAAoB;AACnC,qBAAiB,KAAK,MAAM,SAAS,eAAe;AACpD,IAAAD,MAAK,KAAK,MAAM,SAAS,OAAO;AAAA,EAClC;AACF;AAEA,IAAI,kBAAkB;AAEtB,IAAM,QAAQ;AAAA,EACZ,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,EACX;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,MAAM,CAAC,QAAQ,QAAQ;AAAA,IACvB,UAAU;AAAA,EACZ;AAAA,EACA,OAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS,cAAY;AACnB,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,KAAK;AAAA,IACH,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,eAAe;AAAA,IACb,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,EACX;AACF;AAEA,IAAM,QAAQ;AAAA,EACZ;AAAA,EACA;AAAA,EACA,GAAG,CAAC,GAAG,OAAO,eAAe,GAAG,OAAO,IAAI,EAAE,IAAI,SAAO,IAAI,YAAY,CAAC;AAC3E;AAEA,IAAM,qBAAqB,gBAAgB;AAAA,EACzC,MAAM;AAAA,EAEN,cAAc;AAAA,EAEd;AAAA,EAEA;AAAA,EAEA,OAAO;AACL,WAAO;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,SAAS;AACP,QAAI;AACF,WAAK,QAAQ;AACb,YAAM,EAAE,QAAQ,QAAQ,KAAK,eAAe,UAAU,OAAO,IAAI;AACjE,YAAM,qBAAqB,0BAA0B;AAAA,QACnD;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,WAAK,qBAAqB;AAC1B,YAAM,aAAa,uBAAuB,EAAE,QAAQ,cAAc,CAAC;AACnE,aAAO,mBAAmB,OAAO,GAAG,UAAU;AAAA,IAChD,SAAS,KAAK;AACZ,WAAK,QAAQ;AACb,aAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,MAAM,EAAE,GAAG,IAAI,KAAK;AAAA,IACxD;AAAA,EACF;AAAA,EAEA,UAAU;AACR,QAAI,KAAK,SAAS,QAAQ,KAAK,eAAe,MAAM;AAClD,cAAQ;AAAA,QACN;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,UAAU;AACR,QAAI,KAAK,OAAO;AACd;AAAA,IACF;AAEA,UAAM,EAAE,QAAQ,KAAK,mBAAmB,IAAI;AAC5C,uBAAmB,QAAQ;AAE3B,UAAM,kBAAkB,qBAAqB;AAAA,MAC3C;AAAA,MACA,iBAAiB;AAAA,QACf,eAAe,WAASE,eAAc,KAAK,MAAM,KAAK;AAAA,QACtD,MAAM,WAASF,MAAK,KAAK,MAAM,KAAK;AAAA,QACpC,QAAQ,WAASC,QAAO,KAAK,MAAM,KAAK;AAAA,MAC1C;AAAA,IACF,CAAC;AACD,UAAM,mBAAmB,IAAI,aAAa,IAAI,MAAM,IAAI;AACxD,SAAK,YAAY,IAAI,qBAAS,kBAAkB,eAAe;AAC/D,SAAK,mBAAmB;AACxB,qBAAiB,0BAA0B;AAAA,EAC7C;AAAA,EAEA,UAAU;AACR,SAAK,mBAAmB,QAAQ;AAAA,EAClC;AAAA,EAEA,gBAAgB;AACd,QAAI,KAAK,cAAc,OAAW,MAAK,UAAU,QAAQ;AAAA,EAC3D;AAAA,EAEA,UAAU;AAAA,IACR,WAAW;AACT,YAAM,EAAE,KAAK,IAAI;AACjB,aAAO,OAAO,OAAO,KAAK;AAAA,IAC5B;AAAA,IAEA,SAAS;AACP,YAAM,EAAE,QAAQ,IAAI;AACpB,UAAI,OAAO,YAAY,YAAY;AACjC,eAAO;AAAA,MACT;AACA,aAAO,aAAW,QAAQ,OAAO;AAAA,IACnC;AAAA,EACF;AAAA,EAEA,OAAO;AAAA,IACL,QAAQ;AAAA,MACN,QAAQ,gBAAgB;AACtB,cAAM,EAAE,UAAU,IAAI;AACtB,YAAI,CAAC,UAAW;AAChB,gCAAwB,cAAc,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAChE,oBAAU,OAAO,KAAK,KAAK;AAAA,QAC7B,CAAC;AAAA,MACH;AAAA,MACA,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,SAAS;AAAA,IACP,gBAAgB,YAAY;AAC1B,aAAO,KAAK,mBAAmB,gBAAgB,UAAU,KAAK;AAAA,IAChE;AAAA,IAEA,yCAAyC,YAAY;AAEnD,aAAO,WAAW;AAAA,IACpB;AAAA,IAEA,YAAY,KAAK;AACf,eAAS,MAAM,KAAK,MAAM,UAAU,GAAG,CAAC;AAAA,IAC1C;AAAA,IAEA,UAAU,QAAQ;AAChB,UAAI,KAAK,MAAM;AACb,eAAO,KAAK,IAAI;AAChB;AAAA,MACF;AACA,YAAM,UAAU,CAAC,GAAG,KAAK,UAAU;AACnC,aAAO,OAAO;AACd,WAAK,MAAM,qBAAqB,OAAO;AAAA,IACzC;AAAA,IAEA,aAAa;AACX,YAAM,aAAa,UAAQ,KAAK,OAAO,GAAG,SAAS;AACnD,WAAK,UAAU,UAAU;AAAA,IAC3B;AAAA,IAEA,eAAe,UAAU,UAAU;AACjC,YAAM,iBAAiB,UACrB,KAAK,OAAO,UAAU,GAAG,KAAK,OAAO,UAAU,CAAC,EAAE,CAAC,CAAC;AACtD,WAAK,UAAU,cAAc;AAAA,IAC/B;AAAA,IAEA,+BAA+B,EAAE,IAAI,QAAQ,GAAG;AAC9C,YAAM,YAAY,KAAK,yCAAyC,EAAE;AAClE,UAAI,CAAC,WAAW;AACd,eAAO,EAAE,UAAU;AAAA,MACrB;AACA,YAAM,OAAO,UAAU;AACvB,YAAM,UAAU,EAAE,MAAM,UAAU;AAClC,UAAI,OAAO,WAAW,MAAM;AAC1B,cAAM,cAAc,UAAU,gBAAgB,OAAO,KAAK,CAAC;AAC3D,eAAO,EAAE,GAAG,aAAa,GAAG,QAAQ;AAAA,MACtC;AACA,aAAO;AAAA,IACT;AAAA,IAEA,uBAAuB,UAAU;AAC/B,aAAO,KAAK,mBAAmB;AAAA,QAC7B;AAAA,QACA,KAAK;AAAA,MACP;AAAA,IACF;AAAA,IAEA,YAAY,KAAK;AACf,WAAK,UAAU,KAAK,gBAAgB,IAAI,IAAI;AAC5C,UAAI,KAAK,kBAAkB,KAAK,MAAM,KAAK,QAAQ,OAAO;AAC1D,wBAAkB,IAAI;AAAA,IACxB;AAAA,IAEA,UAAU,KAAK;AACb,YAAM,UAAU,IAAI,KAAK;AACzB,UAAI,YAAY,QAAW;AACzB;AAAA,MACF;AACA,iBAAW,IAAI,IAAI;AACnB,YAAM,WAAW,KAAK,uBAAuB,IAAI,QAAQ;AACzD,WAAK,WAAW,UAAU,GAAG,OAAO;AACpC,YAAM,QAAQ,EAAE,SAAS,SAAS;AAClC,WAAK,YAAY,EAAE,MAAM,CAAC;AAAA,IAC5B;AAAA,IAEA,aAAa,KAAK;AAChB,mBAAa,KAAK,KAAK,IAAI,MAAM,IAAI,QAAQ;AAC7C,UAAI,IAAI,aAAa,SAAS;AAC5B,mBAAW,IAAI,KAAK;AACpB;AAAA,MACF;AACA,YAAM,EAAE,OAAO,UAAU,QAAQ,IAAI,KAAK;AAC1C,WAAK,WAAW,UAAU,CAAC;AAC3B,YAAM,UAAU,EAAE,SAAS,SAAS;AACpC,WAAK,YAAY,EAAE,QAAQ,CAAC;AAAA,IAC9B;AAAA,IAEA,aAAa,KAAK;AAChB,iBAAW,IAAI,IAAI;AACnB,mBAAa,IAAI,MAAM,IAAI,MAAM,IAAI,QAAQ;AAC7C,YAAM,WAAW,KAAK,QAAQ;AAC9B,YAAM,WAAW,KAAK,uBAAuB,IAAI,QAAQ;AACzD,WAAK,eAAe,UAAU,QAAQ;AACtC,YAAM,QAAQ,EAAE,SAAS,KAAK,QAAQ,SAAS,UAAU,SAAS;AAClE,WAAK,YAAY,EAAE,MAAM,CAAC;AAAA,IAC5B;AAAA,IAEA,mBAAmB,gBAAgB,KAAK;AACtC,UAAI,CAAC,eAAe,SAAS;AAC3B,eAAO;AAAA,MACT;AACA,YAAM,cAAc,CAAC,GAAG,IAAI,GAAG,QAAQ,EAAE;AAAA,QACvC,QAAM,GAAG,MAAM,SAAS,MAAM;AAAA,MAChC;AACA,YAAM,kBAAkB,YAAY,QAAQ,IAAI,OAAO;AACvD,YAAM,eAAe,eAAe,UAAU;AAAA,QAC5C;AAAA,MACF;AACA,YAAM,gBAAgB,YAAY,QAAQ,eAAe,MAAM;AAC/D,aAAO,iBAAiB,CAAC,IAAI,kBACzB,eACA,eAAe;AAAA,IACrB;AAAA,IAEA,WAAW,KAAK,eAAe;AAC7B,YAAM,EAAE,MAAM,SAAS,IAAI;AAC3B,UAAI,CAAC,QAAQ,CAAC,UAAU;AACtB,eAAO;AAAA,MACT;AAEA,YAAM,iBAAiB,KAAK,+BAA+B,GAAG;AAC9D,YAAM,cAAc,KAAK,mBAAmB,gBAAgB,GAAG;AAC/D,YAAM,iBAAiB;AAAA,QACrB,GAAG,KAAK;AAAA,QACR;AAAA,MACF;AACA,YAAM,YAAY;AAAA,QAChB,GAAG;AAAA,QACH;AAAA,QACA;AAAA,MACF;AACA,aAAO,KAAK,WAAW,aAAa;AAAA,IACtC;AAAA,IAEA,YAAY;AACV,wBAAkB;AAAA,IACpB;AAAA,EACF;AACF,CAAC;AAED,IAAO,uBAAQ;", "names": ["events", "value", "h", "element", "emit", "manage", "manageAndEmit"]}