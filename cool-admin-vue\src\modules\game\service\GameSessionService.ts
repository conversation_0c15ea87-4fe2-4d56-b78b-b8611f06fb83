import { BaseService } from '/@/cool';

// 游戏场次服务
export default class GameSessionService extends BaseService {
	namespace = '/admin/game/session';

	// 开始游戏
	startGame(data: { id: number }) {
		return this.request({
			url: '/startGame',
			method: 'post',
			data
		});
	}

	// 结束游戏
	endGame(data: { id: number }) {
		return this.request({
			url: '/endGame',
			method: 'post',
			data
		});
	}

	// 取消游戏
	cancelGame(data: { id: number }) {
		return this.request({
			url: '/cancelGame',
			method: 'post',
			data
		});
	}

	// 获取指定日期的场次计划
	getSessionPlan(data: { date: string; themeId?: number }) {
		return this.request({
			url: '/plan',
			method: 'get',
			params: data
		});
	}

	// 生成场次计划
	generateSessionPlan(data: {
		themeId: number;
		date: string;
		businessHours: { start: string; end: string };
		gameDuration: number;
		intervalTime: number;
	}) {
		return this.request({
			url: '/generatePlan',
			method: 'post',
			data
		});
	}

	// 检查时间冲突
	checkTimeConflict(data: {
		themeId: number;
		startTime: string;
		endTime: string;
		excludeId?: number;
	}) {
		return this.request({
			url: '/checkConflict',
			method: 'post',
			data
		});
	}

	// 获取可用时间段
	getAvailableTimeSlots(data: {
		themeId: number;
		date: string;
		duration: number;
	}) {
		return this.request({
			url: '/availableSlots',
			method: 'get',
			params: data
		});
	}

	// 批量更新场次状态
	batchUpdateStatus(data: { ids: number[]; status: number }) {
		return this.request({
			url: '/batchUpdateStatus',
			method: 'post',
			data
		});
	}
}
