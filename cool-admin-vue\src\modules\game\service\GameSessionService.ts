import { BaseService } from '/@/cool';

// 游戏场次服务
export default class GameSessionService extends BaseService {
	namespace = '/admin/game/session';

	// 开始游戏
	startGame(data: { id: number }) {
		return this.request({
			url: '/startGame',
			method: 'post',
			data
		});
	}

	// 结束游戏
	endGame(data: { id: number }) {
		return this.request({
			url: '/endGame',
			method: 'post',
			data
		});
	}

	// 取消游戏
	cancelGame(data: { id: number }) {
		return this.request({
			url: '/cancelGame',
			method: 'post',
			data
		});
	}
}
