import { Body, Inject, Post, Get, Query } from '@midwayjs/core';
import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core';
import { GameSessionEntity } from '../../entity/session';
import { GameSessionService } from '../../service/session';
import { GameSessionCreateDTO, GameSessionUpdateDTO, StartGameDTO } from '../../dto/session';
import { GameThemeEntity } from '../../entity/theme';

/**
 * 游戏场次管理
 */
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: GameSessionEntity,
  service: GameSessionService,
  pageQueryOp: {
    select: ['a.*', 'b.name as themeName'],
    join: [
      {
        entity: GameThemeEntity,
        alias: 'b',
        condition: 'a.themeId = b.id',
        type: 'leftJoin',
      },
    ],
  },
})
export class AdminGameSessionController extends BaseController {
  @Inject()
  gameSessionService: GameSessionService;

  /**
   * 开始游戏
   */
  @Post('/startGame')
  async startGame(@Body() dto: StartGameDTO) {
    return this.ok(await this.gameSessionService.startGame(dto));
  }

  /**
   * 开始游戏计时
   */
  @Post('/startTimer')
  async startGameTimer(@Body() body: { sessionId: number }) {
    return this.ok(await this.gameSessionService.startGameTimer(body.sessionId));
  }

  /**
   * 结束游戏
   */
  @Post('/endGame')
  async endGame(@Body() body: { sessionId: number }) {
    return this.ok(await this.gameSessionService.endGame(body.sessionId));
  }

  /**
   * 取消游戏
   */
  @Post('/cancelGame')
  async cancelGame(@Body() body: { sessionId: number; reason?: string }) {
    const { sessionId, reason } = body;
    return this.ok(await this.gameSessionService.cancelGame(sessionId, reason));
  }

  /**
   * 获取当前进行中的游戏
   */
  @Get('/active')
  async getActiveGames() {
    return this.ok(await this.gameSessionService.getActiveGames());
  }

  /**
   * 获取今日游戏统计
   */
  @Get('/todayStats')
  async getTodayStats() {
    return this.ok(await this.gameSessionService.getTodayStats());
  }

  /**
   * 创建游戏场次
   */
  @Post('/create')
  async createSession(@Body() dto: GameSessionCreateDTO) {
    return this.ok(await this.gameSessionService.add(dto));
  }

  /**
   * 更新游戏场次
   */
  @Post('/updateSession')
  async updateSession(@Body() dto: GameSessionUpdateDTO) {
    return this.ok(await this.gameSessionService.update(dto));
  }

  /**
   * 获取游戏场次详情（包含关联信息）
   */
  @Get('/detail')
  async getDetail(@Query('id') id: number) {
    const session = await this.gameSessionService.info(id);
    if (session) {
      // 获取关联的主题信息
      const theme = await this.gameSessionService.getThemeInfo(session.themeId);
      session.theme = theme;
      
      // 如果有会员ID，获取会员信息
      if (session.memberId) {
        const member = await this.gameSessionService.getMemberInfo(session.memberId);
        session.member = member;
      }
    }
    return this.ok(session);
  }

  /**
   * 批量操作游戏场次
   */
  @Post('/batchOperation')
  async batchOperation(@Body() body: { 
    operation: 'start' | 'end' | 'cancel'; 
    sessionIds: number[]; 
    reason?: string 
  }) {
    const { operation, sessionIds, reason } = body;
    const results = [];

    for (const sessionId of sessionIds) {
      try {
        let result;
        switch (operation) {
          case 'start':
            result = await this.gameSessionService.startGameTimer(sessionId);
            break;
          case 'end':
            result = await this.gameSessionService.endGame(sessionId);
            break;
          case 'cancel':
            result = await this.gameSessionService.cancelGame(sessionId, reason);
            break;
        }
        results.push({ sessionId, success: true, result });
      } catch (error) {
        results.push({ sessionId, success: false, error: error.message });
      }
    }

    return this.ok(results);
  }
}
